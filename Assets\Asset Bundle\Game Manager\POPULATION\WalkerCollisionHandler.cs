using UnityEngine;

public class WalkerCollisionHandler : MonoBehaviour
{
    public Animator walkerAnimator;
    public string deathTrigger = "isDead";

    private bool isDead = false;
    private WaypointWalker waypointWalker;
    private int walkerIndex = -1;
    private Collider walkerCollider;

    void Start()
    {
        if (walkerAnimator == null) walkerAnimator = GetComponent<Animator>();
        walkerCollider = GetComponent<Collider>();
        // Index will be set by WaypointWalker via SetWalkerData()
    }

    public void SetWalkerData(WaypointWalker walker, int index)
    {
        waypointWalker = walker;
        walkerIndex = index;
    }

    void OnTriggerEnter(Collider other)
    {
        if (!isDead && other.CompareTag("Player"))
            TriggerDeath();
    }



    void TriggerDeath()
    {
        isDead = true;
        if (walkerAnimator != null) walkerAnimator.SetTrigger(deathTrigger);
        if (waypointWalker != null && walkerIndex >= 0) waypointWalker.TriggerWalkerDeath(walkerIndex);
        if (walkerCollider != null) walkerCollider.enabled = false;
    }
}
