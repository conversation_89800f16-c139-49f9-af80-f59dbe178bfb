<libraries>
  <library
      name="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release"
      jars="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\7df11da00cd0e96bec3485c9ea7384af\transformed\out\jars\classes.jar;D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\7df11da00cd0e96bec3485c9ea7384af\transformed\out\jars\libs\R.jar;D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\7df11da00cd0e96bec3485c9ea7384af\transformed\out\jars\libs\unity-classes.jar"
      resolved="Gradle:unityLibrary:unspecified"
      folder="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\7df11da00cd0e96bec3485c9ea7384af\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar"
      jars="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar"
      resolved="__local_aars__:D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads:24.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads:24.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-appset:16.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-appset:16.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\af425f2533cb03e8373310f0c6b921bc\transformed\appcompat-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\af425f2533cb03e8373310f0c6b921bc\transformed\appcompat-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-api:24.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-api:24.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.ump:user-messaging-platform:3.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\470b47d8c053be8c559a449ccc33e67a\transformed\jetified-user-messaging-platform-3.2.0\jars\classes.jar"
      resolved="com.google.android.ump:user-messaging-platform:3.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\470b47d8c053be8c559a449ccc33e67a\transformed\jetified-user-messaging-platform-3.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-analytics:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-api:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-api:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.3.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.3.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-impl:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-impl:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-ads-identifier:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\85c2442acd9075fd634013232f05c010\transformed\jetified-firebase-installations-interop-17.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\85c2442acd9075fd634013232f05c010\transformed\jetified-firebase-installations-interop-17.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-sdk-api:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-sdk-api:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-measurement-base:21.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-measurement-base:21.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5d2e05eeb70c1783d7caa312c195de61\transformed\fragment-1.6.0\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5d2e05eeb70c1783d7caa312c195de61\transformed\fragment-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7285772fa78761bf653d71f2456fc699\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7285772fa78761bf653d71f2456fc699\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0f203b4be2356d2269eb5f5cd102f103\transformed\jetified-lifecycle-service-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0f203b4be2356d2269eb5f5cd102f103\transformed\jetified-lifecycle-service-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f54dc8b4025cf67bf92a073756e1e04d\transformed\lifecycle-livedata-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f54dc8b4025cf67bf92a073756e1e04d\transformed\lifecycle-livedata-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.1\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.1"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b00c7617c8b581344fafb8648f1e1156\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b00c7617c8b581344fafb8648f1e1156\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a2adc17748e7372bf9925b8f05d27c5d\transformed\lifecycle-viewmodel-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a2adc17748e7372bf9925b8f05d27c5d\transformed\lifecycle-viewmodel-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c864c6bd1e296df4f88b5762f75d1381\transformed\browser-1.8.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c864c6bd1e296df4f88b5762f75d1381\transformed\browser-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.webkit:webkit:1.11.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\603d6003248eb81eae313742322ddf09\transformed\webkit-1.11.0-alpha02\jars\classes.jar"
      resolved="androidx.webkit:webkit:1.11.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\603d6003248eb81eae313742322ddf09\transformed\webkit-1.11.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\40ee86c1917715cb92b7fc99430effa2\transformed\jetified-appcompat-resources-1.2.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\40ee86c1917715cb92b7fc99430effa2\transformed\jetified-appcompat-resources-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e40ad1ed2331e5b6166d5d3aa8468176\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e40ad1ed2331e5b6166d5d3aa8468176\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\412c542c5c9d3caf0287117536b8e045\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\412c542c5c9d3caf0287117536b8e045\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\974f65ebb6bb8efaeb5b2ca96098c18b\transformed\jetified-ads-adservices-java-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\974f65ebb6bb8efaeb5b2ca96098c18b\transformed\jetified-ads-adservices-java-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\jars\classes.jar"
      resolved="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\jars\classes.jar"
      resolved="androidx.core:core:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7609b3daa41e1a5e2348c9efda853ec7\transformed\lifecycle-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7609b3daa41e1a5e2348c9efda853ec7\transformed\lifecycle-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0e60ee457ea7c0cf39c8b4eb6db14d30\transformed\lifecycle-livedata-core-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0e60ee457ea7c0cf39c8b4eb6db14d30\transformed\lifecycle-livedata-core-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.6.1\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.6.1"/>
  <library
      name="com.google.firebase:firebase-analytics-unity:11.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\88d476129290d346987712ce6db86f46\transformed\jetified-firebase-analytics-unity-11.6.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-analytics-unity:11.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\88d476129290d346987712ce6db86f46\transformed\jetified-firebase-analytics-unity-11.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-app-unity:11.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\da4e061a5877bcf776d414811e40006f\transformed\jetified-firebase-app-unity-11.6.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-app-unity:11.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\da4e061a5877bcf776d414811e40006f\transformed\jetified-firebase-app-unity-11.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":googlemobileads-unity:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\jars\classes.jar"
      resolved=":googlemobileads-unity:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary:GoogleMobileAdsPlugin.androidlib::release"
      jars="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\.transforms\bf60d46d29afd530fc90f3c9d107ca30\transformed\out\jars\classes.jar;D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\.transforms\bf60d46d29afd530fc90f3c9d107ca30\transformed\out\jars\libs\R.jar"
      resolved="Gradle.unityLibrary:GoogleMobileAdsPlugin.androidlib:unspecified"
      folder="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\.transforms\bf60d46d29afd530fc90f3c9d107ca30\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary:FirebaseApp.androidlib::release"
      jars="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\.transforms\5991bb7f9e6ae89274f58a2cd72a9d9e\transformed\out\jars\classes.jar;D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\.transforms\5991bb7f9e6ae89274f58a2cd72a9d9e\transformed\out\jars\libs\R.jar"
      resolved="Gradle.unityLibrary:FirebaseApp.androidlib:unspecified"
      folder="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\.transforms\5991bb7f9e6ae89274f58a2cd72a9d9e\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e930a038c106b8f84838deacd015318f\transformed\jetified-firebase-components-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e930a038c106b8f84838deacd015318f\transformed\jetified-firebase-components-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.2.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.2.5"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\626dd3eb1f2cfdb09b20bf95fae1c5b1\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\626dd3eb1f2cfdb09b20bf95fae1c5b1\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5263fbac2702dc44084b77c5e1ed9cb9\transformed\sqlite-framework-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5263fbac2702dc44084b77c5e1ed9cb9\transformed\sqlite-framework-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4a2425d5037364cedeefbe180e887d41\transformed\sqlite-2.1.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4a2425d5037364cedeefbe180e887d41\transformed\sqlite-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.2.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.2.5\f5e3b73a0c2ab5e276e26868e4ce3542baede207\room-common-2.2.5.jar"
      resolved="androidx.room:room-common:2.2.5"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c8e5ab0fea0c03630c097c3f52c9763c\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c8e5ab0fea0c03630c097c3f52c9763c\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.6.0\a7257339a052df0f91433cf9651231bbb802b502\annotation-jvm-1.6.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.6.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.1\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.21\43d50ab85bc7587adfe3dda3dbe579e5f8d51265\kotlin-stdlib-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.21"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.21\d749cd5ae25da36d06e5028785038e24f9d37976\kotlin-stdlib-common-1.8.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.11.0\c5a0ace696d3f8b1c1d8cc036d8c03cc0cbe6b69\error_prone_annotations-2.11.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.11.0"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"/>
</libraries>
