-- Merging decision tree log ---
manifest
ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
MERGED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-66:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\af425f2533cb03e8373310f0c6b921bc\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\470b47d8c053be8c559a449ccc33e67a\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:17:1-37:12
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-installations-interop:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c2442acd9075fd634013232f05c010\transformed\jetified-firebase-installations-interop-17.0.1\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d2e05eeb70c1783d7caa312c195de61\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7285772fa78761bf653d71f2456fc699\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f203b4be2356d2269eb5f5cd102f103\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f54dc8b4025cf67bf92a073756e1e04d\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b00c7617c8b581344fafb8648f1e1156\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2adc17748e7372bf9925b8f05d27c5d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c864c6bd1e296df4f88b5762f75d1381\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-3\603d6003248eb81eae313742322ddf09\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\40ee86c1917715cb92b7fc99430effa2\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e40ad1ed2331e5b6166d5d3aa8468176\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\412c542c5c9d3caf0287117536b8e045\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\974f65ebb6bb8efaeb5b2ca96098c18b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7609b3daa41e1a5e2348c9efda853ec7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e60ee457ea7c0cf39c8b4eb6db14d30\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.firebase:firebase-analytics-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\88d476129290d346987712ce6db86f46\transformed\jetified-firebase-analytics-unity-11.6.0\AndroidManifest.xml:3:1-8:12
MERGED from [com.google.firebase:firebase-app-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da4e061a5877bcf776d414811e40006f\transformed\jetified-firebase-app-unity-11.6.0\AndroidManifest.xml:3:1-8:12
MERGED from [:googlemobileads-unity:] C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\AndroidManifest.xml:7:1-14:12
MERGED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-22:12
MERGED from [:unityLibrary:FirebaseApp.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e930a038c106b8f84838deacd015318f\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\626dd3eb1f2cfdb09b20bf95fae1c5b1\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5263fbac2702dc44084b77c5e1ed9cb9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2425d5037364cedeefbe180e887d41\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e5ab0fea0c03630c097c3f52c9763c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
	package
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:70-116
	android:versionCode
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:11-69
	android:installLocation
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:117-157
supports-screens
ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-163
	android:largeScreens
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:78-105
	android:smallScreens
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:21-48
	android:normalScreens
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:49-77
	android:xlargeScreens
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:106-134
	android:anyDensity
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:135-160
application
ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-83
MERGED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:5-64:19
MERGED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:5-64:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:14:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:14:5-22:19
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:24:5-37:19
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-20:19
MERGED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-20:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:18-50
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:label
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:16-48
	android:icon
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-80
uses-sdk
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
MERGED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7841b0cd93ac26a6703685554707ae4d\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c6055d2dff4607a9cafb4c530e9689b\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\af425f2533cb03e8373310f0c6b921bc\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\af425f2533cb03e8373310f0c6b921bc\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\470b47d8c053be8c559a449ccc33e67a\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\470b47d8c053be8c559a449ccc33e67a\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\88dfee3f89c01817830d2b8ac8b9631d\transformed\jetified-firebase-analytics-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f8991abf00c737564a8d9adfafe4a9a\transformed\jetified-play-services-measurement-sdk-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c2442acd9075fd634013232f05c010\transformed\jetified-firebase-installations-interop-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-installations-interop:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\85c2442acd9075fd634013232f05c010\transformed\jetified-firebase-installations-interop-17.0.1\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed1cf037394f8d9df052d593dd6985bc\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\618d72e16a0edeb817d4db2b0279c2c4\transformed\jetified-play-services-measurement-base-21.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\87bd1b24c92facc34f46bc29500f1358\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d99f17f794cdec4ef840920492142fba\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d2e05eeb70c1783d7caa312c195de61\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d2e05eeb70c1783d7caa312c195de61\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe943357b5d338bb6d7c3bbeb1ed454c\transformed\jetified-activity-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7285772fa78761bf653d71f2456fc699\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7285772fa78761bf653d71f2456fc699\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\13e0e57dce109f1b01710a3644826884\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\fcfff1cb6db275c65d5464f32efac566\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f203b4be2356d2269eb5f5cd102f103\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0f203b4be2356d2269eb5f5cd102f103\transformed\jetified-lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f54dc8b4025cf67bf92a073756e1e04d\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f54dc8b4025cf67bf92a073756e1e04d\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b00c7617c8b581344fafb8648f1e1156\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\b00c7617c8b581344fafb8648f1e1156\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2adc17748e7372bf9925b8f05d27c5d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a2adc17748e7372bf9925b8f05d27c5d\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\73c962a0f34da5a2fd429ea13553d975\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c864c6bd1e296df4f88b5762f75d1381\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\c864c6bd1e296df4f88b5762f75d1381\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-3\603d6003248eb81eae313742322ddf09\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-3\603d6003248eb81eae313742322ddf09\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\40ee86c1917715cb92b7fc99430effa2\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\40ee86c1917715cb92b7fc99430effa2\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e40ad1ed2331e5b6166d5d3aa8468176\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e40ad1ed2331e5b6166d5d3aa8468176\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\412c542c5c9d3caf0287117536b8e045\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\412c542c5c9d3caf0287117536b8e045\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\974f65ebb6bb8efaeb5b2ca96098c18b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\974f65ebb6bb8efaeb5b2ca96098c18b\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\f712ab45743b1508a2467acccb7632d0\transformed\jetified-core-ktx-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8bba52d669908ffe7e363df39f09f71e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\82fdf85b667d965d5f03e68c8b987206\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7609b3daa41e1a5e2348c9efda853ec7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7609b3daa41e1a5e2348c9efda853ec7\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e60ee457ea7c0cf39c8b4eb6db14d30\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e60ee457ea7c0cf39c8b4eb6db14d30\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-analytics-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\88d476129290d346987712ce6db86f46\transformed\jetified-firebase-analytics-unity-11.6.0\AndroidManifest.xml:7:3-41
MERGED from [com.google.firebase:firebase-analytics-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\88d476129290d346987712ce6db86f46\transformed\jetified-firebase-analytics-unity-11.6.0\AndroidManifest.xml:7:3-41
MERGED from [com.google.firebase:firebase-app-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da4e061a5877bcf776d414811e40006f\transformed\jetified-firebase-app-unity-11.6.0\AndroidManifest.xml:7:3-41
MERGED from [com.google.firebase:firebase-app-unity:11.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\da4e061a5877bcf776d414811e40006f\transformed\jetified-firebase-app-unity-11.6.0\AndroidManifest.xml:7:3-41
MERGED from [:googlemobileads-unity:] C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\AndroidManifest.xml:10:5-44
MERGED from [:googlemobileads-unity:] C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\AndroidManifest.xml:10:5-44
MERGED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-44
MERGED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-44
MERGED from [:unityLibrary:FirebaseApp.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-44
MERGED from [:unityLibrary:FirebaseApp.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\FirebaseApp.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\05c292596d671412479aee05f01fdb8d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e930a038c106b8f84838deacd015318f\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\e930a038c106b8f84838deacd015318f\transformed\jetified-firebase-components-17.1.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\af66ada97217545d02d6c228a333a6e7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\879d949361e78a57a6a7aae7b75cd304\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\626dd3eb1f2cfdb09b20bf95fae1c5b1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\626dd3eb1f2cfdb09b20bf95fae1c5b1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\31b3bde0a14d92a895a4904f71ecd408\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5263fbac2702dc44084b77c5e1ed9cb9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5263fbac2702dc44084b77c5e1ed9cb9\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2425d5037364cedeefbe180e887d41\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4a2425d5037364cedeefbe180e887d41\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b6761725fc2b2b74203d67536ca32d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\08fcff8dca94f664fb9e64d8b790c8ba\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c8119ca7343f5eb9709d91bbcc43e73\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e5ab0fea0c03630c097c3f52c9763c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8e5ab0fea0c03630c097c3f52c9763c\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1f7c1a76f7439ca27df3c52327d1c9ad\transformed\jetified-play-services-ads-24.2.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		INJECTED from D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\147808c858fe6b161814b059620eb414\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-76
uses-permission#android.permission.INTERNET
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:23:5-67
MERGED from [:googlemobileads-unity:] C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\AndroidManifest.xml:12:5-67
MERGED from [:googlemobileads-unity:] C:\Users\<USER>\.gradle\caches\transforms-3\f89ceedd30744c04ca8d72c1c9ca3b1e\transformed\jetified-googlemobileads-unity\AndroidManifest.xml:12:5-67
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:22-64
uses-feature#0x00020000
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-54
	android:glEsVersion
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:19-51
uses-feature#android.hardware.sensor.accelerometer
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-14:36
	android:required
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-61
uses-feature#android.hardware.touchscreen
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:9-52
uses-feature#android.hardware.touchscreen.multitouch
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:5-20:36
	android:required
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:9-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-63
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:5-23:36
	android:required
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:9-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-72
activity#com.unity3d.player.UnityPlayerActivity
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:26:9-47:20
	android:screenOrientation
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-54
	android:launchMode
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-44
	android:hardwareAccelerated
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:13-36
	android:resizeableActivity
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-47
	android:configChanges
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:13-194
	android:theme
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:13-54
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-66
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-39:29
action#android.intent.action.MAIN
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:17-77
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:27-74
meta-data#unityplayer.UnityActivity
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:13-43:40
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:17-37
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:42:17-57
meta-data#android.notch_support
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:13-46:40
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:17-37
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:45:17-53
meta-data#unity.splash-mode
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:9-51:33
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-30
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-45
meta-data#unity.splash-enable
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:9-54:36
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:13-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:53:13-47
meta-data#unity.launch-fullscreen
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:9-57:36
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:13-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-51
meta-data#notch.config
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:58:9-60:50
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:13-47
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:13-40
meta-data#unity.auto-report-fully-drawn
ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:9-63:36
	android:value
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:63:13-33
	android:name
		ADDED from [:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:62:13-57
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:31:9-65
queries
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\93f1d67ad4ae9329ebedec587f199397\transformed\jetified-play-services-measurement-sdk-api-21.3.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:22-65
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:15:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:15:9-21:19
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:30:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:29:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:19:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:25:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:29:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:28:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:26:13-77
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\449fcc9edcecb9ef7f7989ea988e62a7\transformed\jetified-play-services-measurement-impl-21.3.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\81dd96ad7475982a690b5b46895f63bc\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
uses-library#org.apache.http.legacy
ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-12:40
	android:required
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-50
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-16:70
	android:value
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-67
	android:name
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-69
meta-data#com.google.unity.ads.UNITY_VERSION
ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-19:43
	android:value
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:13-40
	android:name
		ADDED from [:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:13-62
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
