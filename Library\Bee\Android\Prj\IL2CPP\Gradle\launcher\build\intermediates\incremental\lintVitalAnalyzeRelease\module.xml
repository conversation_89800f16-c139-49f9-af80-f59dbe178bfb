<lint-module
    format="1"
    dir="D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher"
    name=":launcher"
    type="APP"
    maven="Gradle:launcher:"
    gradle="7.4.2"
    buildFolder="build"
    bootClassPath="C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platforms\android-36\android.jar;C:\Program Files\Unity\Hub\Editor\2021.3.45f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-36"
    neverShrinking="true">
  <lintOptions
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
