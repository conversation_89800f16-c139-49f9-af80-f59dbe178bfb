pluginManagement {
    repositories {
        
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

include ':launcher', ':unityLibrary'
include 'unityLibrary:GoogleMobileAdsPlugin.androidlib'
include 'unityLibrary:FirebaseApp.androidlib'


dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        
        google()
        mavenCentral()
// Android Resolver Repos Start
        def unityProjectPath = $/file:///D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator Cargo/$.replace("\\", "/")
        maven {
            url "https://maven.google.com/" // Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:7, Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:13, Assets/#_AdManager_#/_ADS/Editor/Resources/GoogleMobileAdsDependencies.xml:21, Assets/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:7, Assets/GoogleMobileAds/Editor/GoogleMobileAdsDependencies.xml:12, Assets/GoogleMobileAds/Editor/GoogleUmpDependencies.xml:7
        }
        maven {
            url (unityProjectPath + "/Assets/GeneratedLocalRepo/Firebase/m2repository") // Assets/Firebase/Editor/AnalyticsDependencies.xml:18, Assets/Firebase/Editor/AppDependencies.xml:22
        }
        mavenLocal()
// Android Resolver Repos End
        flatDir {
            dirs "${project(':unityLibrary').projectDir}/libs"
        }
    }
}