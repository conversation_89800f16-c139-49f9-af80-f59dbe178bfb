{"logs": [{"outputFile": "com.smg.tractor.trolly.games.farming.game.launcher-mergeReleaseResources-33:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7609b3daa41e1a5e2348c9efda853ec7\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "665", "startColumns": "4", "startOffsets": "35433", "endColumns": "42", "endOffsets": "35471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\482642211593019ddc82e0ba07d7accc\\transformed\\jetified-play-services-ads-api-24.2.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "2171,2399", "startColumns": "4,4", "startOffsets": "137711,150795", "endLines": "2178,2405", "endColumns": "8,20", "endOffsets": "138104,150970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7841b0cd93ac26a6703685554707ae4d\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,64,65,66,71,72,77,82,83,84,89,90,95,96,101,102,103,109,110,111,116,122,123,126,127,133,134,135,136,139,142,145,146,149,152,153,154,155,156,159,162,163,164,165,171,176,179,182,183,184,189,190,191,194,197,198,201,204,207,210,211,212,215,218,219,224,225,231,236,239,242,243,244,245,246,247,248,249,250,251,252,253,269,275,276,277,278,280,287,293,294,295,298,303,304,312,313,314,315,316,317,319,320,329,330,331,337,338,344,348,349,350,351,352,361,639,664,2944,3000,3165,3293,3299,3303,3452,3597,3730,3746,3771,3794,3797,3800,3803,3830,3857,3874,4174,4182,4195,4211,4215,4245,4258,4262,4272,4282,4336,4420,4444,4536,4607,4644,4679,4731,4748", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,389,445,631,692,983,1035,1085,1138,1186,1237,1292,1352,1417,1476,1538,1590,1651,1713,1759,1892,1944,1994,2045,2452,2819,2864,2923,3120,3177,3372,3553,3607,3664,3856,3914,4110,4166,4360,4417,4468,4690,4742,4797,4987,5203,5253,5399,5455,5661,5722,5782,5852,5985,6116,6244,6312,6441,6567,6629,6692,6760,6827,6950,7075,7142,7207,7272,7561,7742,7863,7984,8050,8117,8327,8396,8462,8587,8713,8780,8906,9033,9158,9285,9341,9406,9532,9655,9720,9928,9995,10283,10463,10583,10703,10768,10830,10892,10956,11018,11077,11137,11198,11259,11318,11378,12038,12289,12340,12389,12437,12555,12847,13077,13124,13184,13290,13470,13524,13859,13913,13969,14015,14062,14113,14213,14265,14595,14654,14708,14946,15001,15203,15342,15388,15443,15488,15532,15880,34093,35388,168489,171084,177202,182867,183242,183409,188639,194765,199614,200365,201219,202089,202155,202234,202309,203093,203984,204803,215531,215936,216407,217198,217361,218722,219286,219439,219898,220316,222709,225032,225720,229727,232505,233248,234669,236876,237618", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,126,132,133,134,135,138,141,144,145,148,151,152,153,154,155,158,161,162,163,164,170,175,178,181,182,183,188,189,190,193,196,197,200,203,206,209,210,211,214,217,218,223,224,230,235,238,241,242,243,244,245,246,247,248,249,250,251,252,268,274,275,276,277,278,286,292,293,294,297,302,303,311,312,313,314,315,316,317,319,328,329,330,336,337,343,347,348,349,350,351,360,364,639,664,2961,3164,3292,3298,3302,3451,3596,3609,3745,3770,3793,3796,3799,3802,3829,3856,3873,3959,4181,4194,4210,4214,4244,4257,4261,4271,4281,4325,4347,4423,4446,4551,4643,4678,4685,4747,4750", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "384,440,626,687,978,1030,1080,1133,1181,1232,1287,1347,1412,1471,1533,1585,1646,1708,1754,1887,1939,1989,2040,2447,2759,2859,2918,3115,3172,3367,3548,3602,3659,3851,3909,4105,4161,4355,4412,4463,4685,4737,4792,4982,5198,5248,5300,5450,5656,5717,5777,5847,5980,6111,6239,6307,6436,6562,6624,6687,6755,6822,6945,7070,7137,7202,7267,7556,7737,7858,7979,8045,8112,8322,8391,8457,8582,8708,8775,8901,9028,9153,9280,9336,9401,9527,9650,9715,9923,9990,10278,10458,10578,10698,10763,10825,10887,10951,11013,11072,11132,11193,11254,11313,11373,12033,12284,12335,12384,12432,12490,12842,13072,13119,13179,13285,13465,13519,13854,13908,13964,14010,14057,14108,14167,14260,14590,14649,14703,14941,14996,15198,15337,15383,15438,15483,15527,15875,16012,34129,35428,169421,177197,182862,183237,183404,188634,194760,195457,200360,201214,202084,202150,202229,202304,203088,203979,204798,208735,215931,216402,217193,217356,218717,219281,219434,219893,220311,222324,223000,225177,225835,230369,233243,234664,235017,237613,237714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f89ceedd30744c04ca8d72c1c9ca3b1e\\transformed\\jetified-googlemobileads-unity\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,36", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "216,259,299,338,377,417,459,497,555,615,804,859,917,968,1022,1076,1126,1172,1246,1302,1388,1471,1511,1549,1604,1647,1720,1767,1962", "endLines": "4,5,6,7,8,9,10,11,12,13,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,38", "endColumns": "42,39,38,38,39,41,37,57,59,39,54,57,50,53,53,49,45,73,55,85,82,39,37,54,42,72,46,46,20", "endOffsets": "258,298,337,376,416,458,496,554,614,654,858,916,967,1021,1075,1125,1171,1245,1301,1387,1470,1510,1548,1603,1646,1719,1766,1813,2074"}, "to": {"startLines": "413,414,415,416,417,418,419,420,421,422,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,4513", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19540,19587,19631,19674,19717,19761,19807,19849,19911,19975,28480,28539,28601,28656,28714,28772,28826,28876,28954,29014,29104,29191,29235,29277,29336,29383,29460,29511,228866", "endLines": "413,414,415,416,417,418,419,420,421,422,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,4515", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,20", "endOffsets": "19582,19626,19669,19712,19756,19802,19844,19906,19970,20014,28534,28596,28651,28709,28767,28821,28871,28949,29009,29099,29186,29230,29272,29331,29378,29455,29506,29557,228978"}}, {"source": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\FirebaseApp.androidlib\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,135,239,346,466,575", "endColumns": "79,103,106,119,108,85", "endOffsets": "130,234,341,461,570,656"}, "to": {"startLines": "733,734,735,736,737,769", "startColumns": "4,4,4,4,4,4", "startOffsets": "41040,41120,41224,41331,41451,44588", "endColumns": "79,103,106,119,108,85", "endOffsets": "41115,41219,41326,41446,41555,44669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c864c6bd1e296df4f88b5762f75d1381\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "391,392,393,394,537,538,728,729,730,731", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "17959,18017,18083,18146,27676,27747,40689,40757,40824,40903", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "18012,18078,18141,18203,27742,27814,40752,40819,40898,40967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5d2e05eeb70c1783d7caa312c195de61\\transformed\\fragment-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "635,644,669,3682,3687", "startColumns": "4,4,4,4,4", "startOffsets": "33932,34340,35640,198446,198616", "endLines": "635,644,669,3686,3690", "endColumns": "56,64,63,24,24", "endOffsets": "33984,34400,35699,198611,198760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1f7c1a76f7439ca27df3c52327d1c9ad\\transformed\\jetified-play-services-ads-24.2.0\\res\\values\\values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "640,641,738,741,744,747,749,751,753,755,757,759,761,763,765,767,770,771,772,773,774,775,776,779", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "34134,34194,41560,41816,42074,42359,42502,42663,42879,43087,43319,43547,43814,43989,44176,44399,44674,44716,44793,44831,44870,44923,44991,45154", "endLines": "640,641,740,743,746,748,750,752,754,756,758,760,762,764,766,768,770,771,772,773,774,775,776,781", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "34189,34239,41811,42069,42354,42497,42658,42874,43082,43314,43542,43809,43984,44171,44394,44583,44711,44788,44826,44865,44918,44986,45025,45318"}}, {"source": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,101,158,214,263,309", "endColumns": "45,56,55,48,45,46", "endOffsets": "96,153,209,258,304,351"}, "to": {"startLines": "662,676,677,678,679,680", "startColumns": "4,4,4,4,4,4", "startOffsets": "35310,36085,36142,36198,36247,36293", "endColumns": "45,56,55,48,45,46", "endOffsets": "35351,36137,36193,36242,36288,36335"}}, {"source": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\launcher\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "5,2,7", "startColumns": "0,0,0", "startOffsets": "190,53,288", "endLines": "6,4,10", "endColumns": "8,8,8", "endOffsets": "286,188,513"}, "to": {"startLines": "1851,2191,2194", "startColumns": "4,4,4", "startOffsets": "113298,139112,139250", "endLines": "1852,2193,2197", "endColumns": "8,8,8", "endOffsets": "113393,139245,139472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a2adc17748e7372bf9925b8f05d27c5d\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "668", "startColumns": "4", "startOffsets": "35590", "endColumns": "49", "endOffsets": "35635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\800f0ebdbf82eee61967fbab7276b7a0\\transformed\\core-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,2086,2145,2204,2264,2324,2384,2444,2504,2564,2624,2684,2744,2804,2863,2923,2983,3043,3103,3163,3223,3283,3343,3403,3463,3522,3582,3642,3701,3760,3819,3878,3937,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,2140,2199,2259,2319,2379,2439,2499,2559,2619,2679,2739,2799,2858,2918,2978,3038,3098,3158,3218,3278,3338,3398,3458,3517,3577,3637,3696,3755,3814,3873,3932,3991,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "279,379,380,437,438,539,540,541,542,543,544,545,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,637,638,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,675,778,2124,2125,2129,2130,2134,2293,2294,2938,2962,3628,3661,3691,3724", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12495,16954,17026,20826,20895,27819,27889,27957,28029,28099,28160,28234,30173,30234,30295,30357,30421,30483,30544,30612,30712,30772,30838,30911,30980,31037,31089,31604,31676,31752,31817,31876,31935,31995,32055,32115,32175,32235,32295,32355,32415,32475,32535,32594,32654,32714,32774,32834,32894,32954,33014,33074,33134,33194,33253,33313,33373,33432,33491,33550,33609,33668,34023,34058,34451,34506,34569,34624,34682,34740,34801,34864,34921,34972,35022,35083,35140,35206,35240,35275,36015,45083,134317,134434,134635,134745,134946,147133,147205,168286,169426,196034,197765,198765,199447", "endLines": "279,379,380,437,438,539,540,541,542,543,544,545,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,596,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,626,627,628,629,630,637,638,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,675,778,2124,2128,2129,2133,2134,2293,2294,2943,2971,3660,3681,3723,3729", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "12550,17021,17109,20890,20953,27884,27952,28024,28094,28155,28229,28302,30229,30290,30352,30416,30478,30539,30607,30707,30767,30833,30906,30975,31032,31084,31146,31671,31747,31812,31871,31930,31990,32050,32110,32170,32230,32290,32350,32410,32470,32530,32589,32649,32709,32769,32829,32889,32949,33009,33069,33129,33189,33248,33308,33368,33427,33486,33545,33604,33663,33722,34053,34088,34501,34564,34619,34677,34735,34796,34859,34916,34967,35017,35078,35135,35201,35235,35270,35305,36080,45149,134429,134630,134740,134941,135070,147200,147267,168484,169722,197760,198441,199442,199609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "674,718", "startColumns": "4,4", "startOffsets": "35947,39396", "endColumns": "67,166", "endOffsets": "36010,39558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\40ee86c1917715cb92b7fc99430effa2\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2416,2432,2438,4424,4440", "startColumns": "4,4,4,4,4", "startOffsets": "151492,151917,152095,225182,225593", "endLines": "2431,2437,2447,4439,4443", "endColumns": "24,24,24,24,24", "endOffsets": "151912,152090,152374,225588,225715"}}, {"source": "D:\\My Project\\Tractor Simulator Cargo Games (V1.9)smg\\Tractor Simulator Cargo\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,3", "startColumns": "2,2", "startOffsets": "55,121", "endColumns": "64,65", "endOffsets": "117,184"}, "to": {"startLines": "709,732", "startColumns": "4,4", "startOffsets": "38289,40972", "endColumns": "66,67", "endOffsets": "38351,41035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fe943357b5d338bb6d7c3bbeb1ed454c\\transformed\\jetified-activity-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "59", "endOffsets": "110"}, "to": {"startLines": "666", "startColumns": "4", "startOffsets": "35476", "endColumns": "59", "endOffsets": "35531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\81dd96ad7475982a690b5b46895f63bc\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "708", "startColumns": "4", "startOffsets": "38206", "endColumns": "82", "endOffsets": "38284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1b2a8be1f7c20051c0ee2c7353491a0\\transformed\\jetified-play-services-base-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "397,398,399,400,401,402,403,404,710,711,712,713,714,715,716,717,719,720,721,722,723,724,725,726,727,3996,4389", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18323,18413,18493,18583,18673,18753,18834,18914,38356,38461,38642,38767,38874,39054,39177,39293,39563,39751,39856,40037,40162,40337,40485,40548,40610,209953,224303", "endLines": "397,398,399,400,401,402,403,404,710,711,712,713,714,715,716,717,719,720,721,722,723,724,725,726,727,4008,4407", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "18408,18488,18578,18668,18748,18829,18909,18989,38456,38637,38762,38869,39049,39172,39288,39391,39746,39851,40032,40157,40332,40480,40543,40605,40684,210263,224715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af425f2533cb03e8373310f0c6b921bc\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "63,124,125,318,365,366,367,372,373,374,375,376,377,378,381,382,383,384,385,386,387,388,389,390,395,396,405,406,407,408,409,410,411,412,423,424,425,426,427,428,429,430,431,432,433,434,435,436,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,546,547,566,567,568,569,570,571,572,588,589,590,591,592,593,594,595,631,632,633,634,636,642,643,645,663,670,671,672,673,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,777,782,783,784,785,786,787,795,796,800,804,808,813,819,826,830,834,839,843,847,851,855,859,863,869,873,879,883,889,893,898,902,905,909,915,919,925,929,935,938,942,946,950,954,958,959,960,961,964,967,970,973,977,978,979,980,981,984,986,988,990,995,996,1000,1006,1010,1011,1013,1024,1025,1029,1035,1039,1040,1041,1045,1072,1076,1077,1081,1109,1278,1304,1473,1499,1530,1538,1544,1558,1580,1585,1590,1600,1609,1618,1622,1629,1637,1644,1645,1654,1657,1660,1664,1668,1672,1675,1676,1681,1686,1696,1701,1708,1714,1715,1718,1722,1727,1729,1731,1734,1737,1739,1743,1746,1753,1756,1759,1763,1765,1769,1771,1773,1775,1779,1787,1795,1807,1813,1822,1825,1836,1839,1840,1845,1846,1853,1922,1992,1993,2003,2012,2013,2015,2019,2022,2025,2028,2031,2034,2037,2040,2044,2047,2050,2053,2057,2060,2064,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2090,2092,2093,2094,2095,2096,2097,2098,2099,2101,2102,2104,2105,2107,2109,2110,2112,2113,2114,2115,2116,2117,2119,2120,2121,2122,2123,2135,2137,2139,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2155,2156,2157,2158,2159,2160,2161,2163,2167,2179,2180,2181,2182,2183,2184,2188,2189,2190,2198,2200,2202,2204,2206,2208,2209,2210,2211,2213,2215,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2231,2232,2233,2234,2236,2238,2239,2241,2242,2244,2246,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2259,2261,2262,2263,2264,2266,2267,2268,2269,2270,2272,2274,2276,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2295,2370,2373,2376,2379,2393,2406,2448,2477,2504,2513,2575,2934,2972,3610,3960,3984,3990,4009,4030,4154,4326,4332,4348,4354,4408,4447,4516,4552,4686,4698,4724", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2764,5305,5350,14172,16017,16072,16131,16453,16517,16587,16648,16723,16799,16876,17114,17199,17281,17357,17433,17510,17588,17694,17800,17879,18208,18265,18994,19068,19143,19208,19274,19334,19395,19467,20019,20086,20154,20213,20272,20331,20390,20449,20503,20557,20610,20664,20718,20772,20958,21032,21111,21184,21258,21329,21401,21473,21546,21603,21661,21734,21808,21882,21957,22029,22102,22172,22243,22303,22364,22433,22502,22572,22646,22722,22786,22863,22939,23016,23081,23150,23227,23302,23371,23439,23516,23582,23643,23740,23805,23874,23973,24044,24103,24161,24218,24277,24341,24412,24484,24556,24628,24700,24767,24835,24903,24962,25025,25089,25179,25270,25330,25396,25463,25529,25599,25663,25716,25783,25844,25911,26024,26082,26145,26210,26275,26350,26423,26495,26544,26605,26666,26727,26789,26853,26917,26981,27046,27109,27169,27230,27296,27355,27415,27477,27548,27608,28307,28393,29562,29652,29739,29827,29909,29992,30082,31151,31203,31261,31306,31372,31436,31493,31550,33727,33784,33832,33881,33989,34244,34291,34405,35356,35704,35768,35830,35890,36340,36414,36484,36562,36616,36686,36771,36819,36865,36926,36989,37055,37119,37190,37253,37318,37382,37443,37504,37556,37629,37703,37772,37847,37921,37995,38136,45030,45323,45401,45491,45579,45675,45765,46347,46436,46683,46964,47216,47501,47894,48371,48593,48815,49091,49318,49548,49778,50008,50238,50465,50884,51110,51535,51765,52193,52412,52695,52903,53034,53261,53687,53912,54339,54560,54985,55105,55381,55682,56006,56297,56611,56748,56879,56984,57226,57393,57597,57805,58076,58188,58300,58405,58522,58736,58882,59022,59108,59456,59544,59790,60208,60457,60539,60637,61229,61329,61581,62005,62260,62354,62443,62680,64704,64946,65048,65301,67457,77898,79414,89953,91481,93238,93864,94284,95345,96610,96866,97102,97649,98143,98748,98946,99526,100090,100465,100583,101121,101278,101474,101747,102003,102173,102314,102378,102743,103110,103786,104050,104388,104741,104835,105021,105327,105589,105714,105841,106080,106291,106410,106603,106780,107235,107416,107538,107797,107910,108097,108199,108306,108435,108710,109218,109714,110591,110885,111455,111604,112336,112508,112592,112928,113020,113398,118644,124033,124095,124673,125257,125348,125461,125690,125850,126002,126173,126339,126508,126675,126838,127081,127251,127424,127595,127869,128068,128273,128603,128687,128783,128879,128977,129077,129179,129281,129383,129485,129587,129687,129783,129895,130024,130147,130278,130409,130507,130621,130715,130855,130989,131085,131197,131297,131413,131509,131621,131721,131861,131997,132161,132291,132449,132599,132740,132884,133019,133131,133281,133409,133537,133673,133805,133935,134065,134177,135075,135221,135365,135503,135569,135659,135735,135839,135929,136031,136139,136247,136347,136427,136519,136617,136727,136779,136857,136963,137055,137159,137269,137391,137554,138109,138189,138289,138379,138489,138579,138820,138914,139020,139477,139577,139689,139803,139919,140035,140129,140243,140355,140457,140577,140699,140781,140885,141005,141131,141229,141323,141411,141523,141639,141761,141873,142048,142164,142250,142342,142454,142578,142645,142771,142839,142967,143111,143239,143308,143403,143518,143631,143730,143839,143950,144061,144162,144267,144367,144497,144588,144711,144805,144917,145003,145107,145203,145291,145409,145513,145617,145743,145831,145939,146039,146129,146239,146323,146425,146509,146563,146627,146733,146819,146929,147013,147272,149888,150006,150121,150201,150562,150975,152379,153723,155084,155472,158247,168151,169727,195462,208740,209491,209753,210268,210647,214925,222329,222558,223005,223220,224720,225840,228983,230374,235022,235362,236673", "endLines": "63,124,125,318,365,366,367,372,373,374,375,376,377,378,381,382,383,384,385,386,387,388,389,390,395,396,405,406,407,408,409,410,411,412,423,424,425,426,427,428,429,430,431,432,433,434,435,436,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,546,547,566,567,568,569,570,571,572,588,589,590,591,592,593,594,595,631,632,633,634,636,642,643,645,663,670,671,672,673,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,704,705,706,707,777,782,783,784,785,786,794,795,799,803,807,812,818,825,829,833,838,842,846,850,854,858,862,868,872,878,882,888,892,897,901,904,908,914,918,924,928,934,937,941,945,949,953,957,958,959,960,963,966,969,972,976,977,978,979,980,983,985,987,989,994,995,999,1005,1009,1010,1012,1023,1024,1028,1034,1038,1039,1040,1044,1071,1075,1076,1080,1108,1277,1303,1472,1498,1529,1537,1543,1557,1579,1584,1589,1599,1608,1617,1621,1628,1636,1643,1644,1653,1656,1659,1663,1667,1671,1674,1675,1680,1685,1695,1700,1707,1713,1714,1717,1721,1726,1728,1730,1733,1736,1738,1742,1745,1752,1755,1758,1762,1764,1768,1770,1772,1774,1778,1786,1794,1806,1812,1821,1824,1835,1838,1839,1844,1845,1850,1921,1991,1992,2002,2011,2012,2014,2018,2021,2024,2027,2030,2033,2036,2039,2043,2046,2049,2052,2056,2059,2063,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2089,2091,2092,2093,2094,2095,2096,2097,2098,2100,2101,2103,2104,2106,2108,2109,2111,2112,2113,2114,2115,2116,2118,2119,2120,2121,2122,2123,2136,2138,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2154,2155,2156,2157,2158,2159,2160,2162,2166,2170,2179,2180,2181,2182,2183,2187,2188,2189,2190,2199,2201,2203,2205,2207,2208,2209,2210,2212,2214,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2227,2230,2231,2232,2233,2235,2237,2238,2240,2241,2243,2245,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2258,2260,2261,2262,2263,2265,2266,2267,2268,2269,2271,2273,2275,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2291,2292,2369,2372,2375,2378,2392,2398,2415,2476,2503,2512,2574,2933,2937,2999,3627,3983,3989,3995,4029,4153,4173,4331,4335,4353,4388,4419,4512,4535,4606,4697,4723,4730", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2814,5345,5394,14208,16067,16126,16188,16512,16582,16643,16718,16794,16871,16949,17194,17276,17352,17428,17505,17583,17689,17795,17874,17954,18260,18318,19063,19138,19203,19269,19329,19390,19462,19535,20081,20149,20208,20267,20326,20385,20444,20498,20552,20605,20659,20713,20767,20821,21027,21106,21179,21253,21324,21396,21468,21541,21598,21656,21729,21803,21877,21952,22024,22097,22167,22238,22298,22359,22428,22497,22567,22641,22717,22781,22858,22934,23011,23076,23145,23222,23297,23366,23434,23511,23577,23638,23735,23800,23869,23968,24039,24098,24156,24213,24272,24336,24407,24479,24551,24623,24695,24762,24830,24898,24957,25020,25084,25174,25265,25325,25391,25458,25524,25594,25658,25711,25778,25839,25906,26019,26077,26140,26205,26270,26345,26418,26490,26539,26600,26661,26722,26784,26848,26912,26976,27041,27104,27164,27225,27291,27350,27410,27472,27543,27603,27671,28388,28475,29647,29734,29822,29904,29987,30077,30168,31198,31256,31301,31367,31431,31488,31545,31599,33779,33827,33876,33927,34018,34286,34335,34446,35383,35763,35825,35885,35942,36409,36479,36557,36611,36681,36766,36814,36860,36921,36984,37050,37114,37185,37248,37313,37377,37438,37499,37551,37624,37698,37767,37842,37916,37990,38131,38201,45078,45396,45486,45574,45670,45760,46342,46431,46678,46959,47211,47496,47889,48366,48588,48810,49086,49313,49543,49773,50003,50233,50460,50879,51105,51530,51760,52188,52407,52690,52898,53029,53256,53682,53907,54334,54555,54980,55100,55376,55677,56001,56292,56606,56743,56874,56979,57221,57388,57592,57800,58071,58183,58295,58400,58517,58731,58877,59017,59103,59451,59539,59785,60203,60452,60534,60632,61224,61324,61576,62000,62255,62349,62438,62675,64699,64941,65043,65296,67452,77893,79409,89948,91476,93233,93859,94279,95340,96605,96861,97097,97644,98138,98743,98941,99521,100085,100460,100578,101116,101273,101469,101742,101998,102168,102309,102373,102738,103105,103781,104045,104383,104736,104830,105016,105322,105584,105709,105836,106075,106286,106405,106598,106775,107230,107411,107533,107792,107905,108092,108194,108301,108430,108705,109213,109709,110586,110880,111450,111599,112331,112503,112587,112923,113015,113293,118639,124028,124090,124668,125252,125343,125456,125685,125845,125997,126168,126334,126503,126670,126833,127076,127246,127419,127590,127864,128063,128268,128598,128682,128778,128874,128972,129072,129174,129276,129378,129480,129582,129682,129778,129890,130019,130142,130273,130404,130502,130616,130710,130850,130984,131080,131192,131292,131408,131504,131616,131716,131856,131992,132156,132286,132444,132594,132735,132879,133014,133126,133276,133404,133532,133668,133800,133930,134060,134172,134312,135216,135360,135498,135564,135654,135730,135834,135924,136026,136134,136242,136342,136422,136514,136612,136722,136774,136852,136958,137050,137154,137264,137386,137549,137706,138184,138284,138374,138484,138574,138815,138909,139015,139107,139572,139684,139798,139914,140030,140124,140238,140350,140452,140572,140694,140776,140880,141000,141126,141224,141318,141406,141518,141634,141756,141868,142043,142159,142245,142337,142449,142573,142640,142766,142834,142962,143106,143234,143303,143398,143513,143626,143725,143834,143945,144056,144157,144262,144362,144492,144583,144706,144800,144912,144998,145102,145198,145286,145404,145508,145612,145738,145826,145934,146034,146124,146234,146318,146420,146504,146558,146622,146728,146814,146924,147008,147128,149883,150001,150116,150196,150557,150790,151487,153718,155079,155467,158242,168146,168281,171079,196029,209486,209748,209948,210642,214920,215526,222553,222704,223215,224298,225027,228861,229722,232500,235357,236668,236871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3d36d9d7663bf6dc4c74476f3c6323ec\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "368,369,370,371", "startColumns": "4,4,4,4", "startOffsets": "16193,16258,16328,16392", "endColumns": "64,69,63,60", "endOffsets": "16253,16323,16387,16448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7285772fa78761bf653d71f2456fc699\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "667", "startColumns": "4", "startOffsets": "35536", "endColumns": "53", "endOffsets": "35585"}}]}]}