ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp-Editor.dll:
    - BRT_BuildReportWindow
    Assembly-CSharp-firstpass.dll:
    - DG.Tweening.DOTweenAnimation
    Assembly-CSharp.dll:
    - AdmobManager
    - AdsController
    - AlertMessage
    - AutoTypeText
    - BannerAD
    - Bird<PERSON>iger
    - ButtonChildToggle
    - COMPAD
    - DistanceFromPlayer
    - Fail
    - FirebaseAnalytics
    - GameOptimizer
    - GoogleMobileAdsConsentController
    - ImageArrayDisplay
    - LivingOcean
    - LoadingAD
    - LoadingLogic
    - MainMenu
    - PauseAd
    - RCC_Camera
    - RCC_CarControllerV3
    - RCC_ChangableWheels
    - RCC_Chassis
    - RCC_CinematicCamera
    - RCC_ColorPickerBySliders
    - RCC_CustomizerExample
    - RCC_DashboardColors
    - RCC_DashboardInputs
    - RCC_Demo
    - RCC_Exhaust
    - RCC_FOVForCinematicCamera
    - RCC_GroundMaterials
    - RCC_HoodCamera
    - RCC_InfoLabel
    - RCC_Light
    - RCC_Mirror
    - RCC_MobileButtons
    - RCC_MobileUIDrag
    - RCC_Records
    - RCC_SceneManager
    - RCC_Settings
    - RCC_Skidmarks
    - RCC_SkidmarksManager
    - RCC_TrailerAttachPoint
    - RCC_TruckTrailer
    - RCC_UIController
    - RCC_UIDashboardButton
    - RCC_UIDashboardDisplay
    - RCC_UIJoystick
    - RCC_UISliderTextReader
    - RCC_UISteeringWheelController
    - RCC_Useless
    - RCC_Vehicles
    - RCC_WheelCollider
    - SMGGameManager
    - SaveButtonController
    - ScrollUV
    - SenseTraffic
    - SettingAD
    - SliderRelease
    - Tractorsense
    - WPC
    - WPC_Waypoint
    - Waypoint
    - WaypointMover
    - WaypointWalker
    - WaypointsHolder
    - WeatherSystem
    - cronspray
    - manger2
    - player
    - playermain
    - starFxController
    - startscene
    - wheelAi
    - wheelrotator
    Cinemachine.dll:
    - Cinemachine.CinemachineBrain
    - Cinemachine.CinemachineComposer
    - Cinemachine.CinemachinePipeline
    - Cinemachine.CinemachineTransposer
    - Cinemachine.CinemachineVirtualCamera
    - CinemachineTrack
    DOTween.dll:
    - DG.Tweening.Core.DOTweenSettings
    GoogleMobileAds.Common.dll:
    - GoogleMobileAds.Common.MobileAdsEventExecutor
    GoogleMobileAds.Editor.dll:
    - GoogleMobileAds.Editor.GoogleMobileAdsSettings
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TextMeshProUGUI
    Unity.Timeline.Editor.dll:
    - TimelinePreferences
    - UnityEditor.Timeline.SequenceHierarchy
    - UnityEditor.Timeline.TimelineWindow
    Unity.Timeline.dll:
    - UnityEngine.Timeline.ActivationPlayableAsset
    - UnityEngine.Timeline.ActivationTrack
    - UnityEngine.Timeline.AnimationTrack
    - UnityEngine.Timeline.AudioPlayableAsset
    - UnityEngine.Timeline.AudioTrack
    - UnityEngine.Timeline.GroupTrack
    - UnityEngine.Timeline.TimelineAsset
    UnityEditor.Graphs.dll:
    - UnityEditor.Graphs.AnimationBlendTree.Graph
    - UnityEditor.Graphs.AnimationBlendTree.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.AnyStateNode
    - UnityEditor.Graphs.AnimationStateMachine.Graph
    - UnityEditor.Graphs.AnimationStateMachine.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.StateMachineInspector
    - UnityEditor.Graphs.AnimationStateMachine.StateNode
    - UnityEditor.Graphs.AnimatorControllerTool
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.EventTrigger
    - UnityEngine.EventSystems.StandaloneInputModule
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.Dropdown
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.Image
    - UnityEngine.UI.InputField
    - UnityEngine.UI.Mask
    - UnityEngine.UI.Outline
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Slider
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
  serializedClasses:
    Assembly-CSharp:
    - AdmobManager/_InterstitialAd
    - AdmobManager/_RewardedAd
    - AdmobManager/_RewardedInterstitalAd
    - AdmobManager/_appOpen
    - AdmobManager/_bannerAd
    - AdsController/_loadingAdStuff
    - RCC_CarControllerV3/ConfigureVehicleSubsteps
    - RCC_CarControllerV3/Gear
    - RCC_ChangableWheels/ChangableWheels
    - RCC_GroundMaterials/GroundMaterialFrictions
    - RCC_GroundMaterials/TerrainFrictions
    - RCC_GroundMaterials/TerrainFrictions/SplatmapIndexes
    - RCC_Settings/BehaviorType
    - RCC_TruckTrailer/TrailerWheel
    - WaypointMover/UsedAxis
    Cinemachine:
    - Cinemachine.CinemachineBlendDefinition
    - Cinemachine.CinemachineBrain/BrainEvent
    - Cinemachine.CinemachineBrain/VcamActivatedEvent
    - Cinemachine.CinemachineVirtualCameraBase/TransitionParams
    - Cinemachine.LensSettings
    DOTween:
    - DG.Tweening.Core.DOTweenSettings/ModulesSetup
    - DG.Tweening.Core.DOTweenSettings/SafeModeOptions
    Unity.TextMeshPro:
    - TMPro.FaceInfo_Legacy
    - TMPro.FontAssetCreationSettings
    - TMPro.KerningTable
    - TMPro.TMP_Character
    - TMPro.TMP_FontFeatureTable
    - TMPro.TMP_FontWeightPair
    - TMPro.VertexGradient
    Unity.Timeline:
    - UnityEngine.Timeline.AudioClipProperties
    - UnityEngine.Timeline.AudioMixerProperties
    - UnityEngine.Timeline.MarkerList
    - UnityEngine.Timeline.TimelineAsset/EditorSettings
    - UnityEngine.Timeline.TimelineClip
    UnityEngine.CoreModule:
    - UnityEngine.Events.ArgumentCache
    - UnityEngine.Events.PersistentCallGroup
    - UnityEngine.Events.PersistentListenerMode
    - UnityEngine.Events.UnityEvent
    UnityEngine.TextCoreFontEngineModule:
    - UnityEngine.TextCore.FaceInfo
    - UnityEngine.TextCore.Glyph
    - UnityEngine.TextCore.GlyphMetrics
    - UnityEngine.TextCore.GlyphRect
    UnityEngine.UI:
    - UnityEngine.UI.AnimationTriggers
    - UnityEngine.UI.Button/ButtonClickedEvent
    - UnityEngine.UI.ColorBlock
    - UnityEngine.UI.Dropdown/DropdownEvent
    - UnityEngine.UI.Dropdown/OptionData
    - UnityEngine.UI.Dropdown/OptionDataList
    - UnityEngine.UI.FontData
    - UnityEngine.UI.InputField/EndEditEvent
    - UnityEngine.UI.InputField/OnChangeEvent
    - UnityEngine.UI.InputField/SubmitEvent
    - UnityEngine.UI.MaskableGraphic/CullStateChangedEvent
    - UnityEngine.UI.Navigation
    - UnityEngine.UI.ScrollRect/ScrollRectEvent
    - UnityEngine.UI.Scrollbar/ScrollEvent
    - UnityEngine.UI.Slider/SliderEvent
    - UnityEngine.UI.SpriteState
    - UnityEngine.UI.Toggle/ToggleEvent
  methodsToPreserve:
  - assembly: Assembly-CSharp
    fullTypeName: startscene
    methodName: privacy
  - assembly: Assembly-CSharp
    fullTypeName: startscene
    methodName: accept
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: filldec
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: moregams
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: save
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: mode2
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: modeback
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: privacy
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: trainadd
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: fill
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SaveButtonController
    methodName: Save
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: lvlback
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: rateus
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: yes
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: caremode
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: exit
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: moregams
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: No
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: play
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Tilt
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: fl
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: lvl2back
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Btns
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Steer
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: select
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: fldec
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: setting
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Lvel
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: MainMenu
    methodName: Levels
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: pause
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: restart
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: Tilt
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: WeatherRain
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: Ad
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: Btns
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: cronspray
    methodName: Skip
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: Steer
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearCambersBySlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UIDashboardButton
    methodName: GearDrive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Stop
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: home
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: WeatherAir
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: Ad
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: PlayMusic
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: changcontrol
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: StopMusic
  - assembly: Assembly-CSharp
    fullTypeName: RCC_UIDashboardButton
    methodName: Gearreverse
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: Weathersunny
  - assembly: Assembly-CSharp
    fullTypeName: WeatherSystem
    methodName: Ad
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: ok
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: restart
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: resume
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: ChangeWheelsBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTurboByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSH
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetNOSByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetCounterSteeringByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionsSpringForceBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetExhaustFlameByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionsSpringDamperBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTCS
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionDistancesBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSmokeColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetClutchThresholdBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetESP
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionDistancesBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: TogglePreviewExhaustFlameByToggle
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetGearShiftingThresholdBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetTransmission
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: TogglePreviewSmokeByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontCambersBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetFrontSuspensionsSpringDamperBySlider
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRearSuspensionsSpringForceBySlider
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetABS
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetClutchMarginByToggle
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetRevLimiterByToggle
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetHeadlightColorByColorPicker
  - assembly: Assembly-CSharp
    fullTypeName: RCC_CustomizerExample
    methodName: SetSHStrength
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: next
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: home
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: SMGGameManager
    methodName: restart
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: pause
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: pause
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: restart
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: restart
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: home
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: resume
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: home
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: next
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: restart
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: Assembly-CSharp
    fullTypeName: manger2
    methodName: home
  - assembly: UnityEngine.AudioModule
    fullTypeName: UnityEngine.AudioSource
    methodName: Play
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  - assembly: UnityEngine.CoreModule
    fullTypeName: UnityEngine.GameObject
    methodName: SetActive
  sceneClasses:
    Assets/Scenes/MAINMENU.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16188}
    - Class: 114
      Script: {instanceID: 16190}
    - Class: 114
      Script: {instanceID: 16192}
    - Class: 114
      Script: {instanceID: 16440}
    - Class: 114
      Script: {instanceID: 16966}
    - Class: 114
      Script: {instanceID: 17568}
    - Class: 114
      Script: {instanceID: 17586}
    - Class: 114
      Script: {instanceID: 17816}
    - Class: 114
      Script: {instanceID: 17876}
    - Class: 114
      Script: {instanceID: 18094}
    - Class: 114
      Script: {instanceID: 18108}
    - Class: 114
      Script: {instanceID: 18496}
    - Class: 114
      Script: {instanceID: 18584}
    - Class: 114
      Script: {instanceID: 18778}
    - Class: 114
      Script: {instanceID: 19100}
    - Class: 114
      Script: {instanceID: 19106}
    - Class: 114
      Script: {instanceID: 19284}
    - Class: 114
      Script: {instanceID: 19306}
    - Class: 114
      Script: {instanceID: 19502}
    - Class: 114
      Script: {instanceID: 20078}
    - Class: 114
      Script: {instanceID: 20440}
    - Class: 114
      Script: {instanceID: 20530}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 320
      Script: {instanceID: 0}
    Assets/Scenes/gameplay.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 111
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16188}
    - Class: 114
      Script: {instanceID: 16190}
    - Class: 114
      Script: {instanceID: 16192}
    - Class: 114
      Script: {instanceID: 16216}
    - Class: 114
      Script: {instanceID: 16294}
    - Class: 114
      Script: {instanceID: 16408}
    - Class: 114
      Script: {instanceID: 16432}
    - Class: 114
      Script: {instanceID: 16480}
    - Class: 114
      Script: {instanceID: 16676}
    - Class: 114
      Script: {instanceID: 16698}
    - Class: 114
      Script: {instanceID: 16764}
    - Class: 114
      Script: {instanceID: 16782}
    - Class: 114
      Script: {instanceID: 16828}
    - Class: 114
      Script: {instanceID: 16838}
    - Class: 114
      Script: {instanceID: 16864}
    - Class: 114
      Script: {instanceID: 16888}
    - Class: 114
      Script: {instanceID: 16896}
    - Class: 114
      Script: {instanceID: 16966}
    - Class: 114
      Script: {instanceID: 17098}
    - Class: 114
      Script: {instanceID: 17318}
    - Class: 114
      Script: {instanceID: 17360}
    - Class: 114
      Script: {instanceID: 17390}
    - Class: 114
      Script: {instanceID: 17488}
    - Class: 114
      Script: {instanceID: 17568}
    - Class: 114
      Script: {instanceID: 17584}
    - Class: 114
      Script: {instanceID: 17586}
    - Class: 114
      Script: {instanceID: 17620}
    - Class: 114
      Script: {instanceID: 17640}
    - Class: 114
      Script: {instanceID: 17656}
    - Class: 114
      Script: {instanceID: 17664}
    - Class: 114
      Script: {instanceID: 17674}
    - Class: 114
      Script: {instanceID: 17678}
    - Class: 114
      Script: {instanceID: 17702}
    - Class: 114
      Script: {instanceID: 17816}
    - Class: 114
      Script: {instanceID: 17876}
    - Class: 114
      Script: {instanceID: 17916}
    - Class: 114
      Script: {instanceID: 17942}
    - Class: 114
      Script: {instanceID: 17968}
    - Class: 114
      Script: {instanceID: 18094}
    - Class: 114
      Script: {instanceID: 18108}
    - Class: 114
      Script: {instanceID: 18258}
    - Class: 114
      Script: {instanceID: 18484}
    - Class: 114
      Script: {instanceID: 18496}
    - Class: 114
      Script: {instanceID: 18572}
    - Class: 114
      Script: {instanceID: 18690}
    - Class: 114
      Script: {instanceID: 18760}
    - Class: 114
      Script: {instanceID: 18810}
    - Class: 114
      Script: {instanceID: 18862}
    - Class: 114
      Script: {instanceID: 18904}
    - Class: 114
      Script: {instanceID: 18926}
    - Class: 114
      Script: {instanceID: 18950}
    - Class: 114
      Script: {instanceID: 18952}
    - Class: 114
      Script: {instanceID: 18966}
    - Class: 114
      Script: {instanceID: 19006}
    - Class: 114
      Script: {instanceID: 19068}
    - Class: 114
      Script: {instanceID: 19080}
    - Class: 114
      Script: {instanceID: 19100}
    - Class: 114
      Script: {instanceID: 19106}
    - Class: 114
      Script: {instanceID: 19138}
    - Class: 114
      Script: {instanceID: 19306}
    - Class: 114
      Script: {instanceID: 19380}
    - Class: 114
      Script: {instanceID: 19436}
    - Class: 114
      Script: {instanceID: 19466}
    - Class: 114
      Script: {instanceID: 19502}
    - Class: 114
      Script: {instanceID: 19666}
    - Class: 114
      Script: {instanceID: 19702}
    - Class: 114
      Script: {instanceID: 19794}
    - Class: 114
      Script: {instanceID: 19834}
    - Class: 114
      Script: {instanceID: 19872}
    - Class: 114
      Script: {instanceID: 20092}
    - Class: 114
      Script: {instanceID: 20366}
    - Class: 114
      Script: {instanceID: 20434}
    - Class: 114
      Script: {instanceID: 20440}
    - Class: 114
      Script: {instanceID: 20458}
    - Class: 114
      Script: {instanceID: 20638}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 124
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 134
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 146
      Script: {instanceID: 0}
    - Class: 153
      Script: {instanceID: 0}
    - Class: 154
      Script: {instanceID: 0}
    - Class: 156
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 205
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 218
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    - Class: 320
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
    - Class: 1953259897
      Script: {instanceID: 0}
    Assets/Scenes/spleshscene.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16188}
    - Class: 114
      Script: {instanceID: 16190}
    - Class: 114
      Script: {instanceID: 16192}
    - Class: 114
      Script: {instanceID: 16210}
    - Class: 114
      Script: {instanceID: 16268}
    - Class: 114
      Script: {instanceID: 16292}
    - Class: 114
      Script: {instanceID: 16408}
    - Class: 114
      Script: {instanceID: 16480}
    - Class: 114
      Script: {instanceID: 16566}
    - Class: 114
      Script: {instanceID: 16668}
    - Class: 114
      Script: {instanceID: 16676}
    - Class: 114
      Script: {instanceID: 16698}
    - Class: 114
      Script: {instanceID: 16720}
    - Class: 114
      Script: {instanceID: 16764}
    - Class: 114
      Script: {instanceID: 16838}
    - Class: 114
      Script: {instanceID: 16890}
    - Class: 114
      Script: {instanceID: 16966}
    - Class: 114
      Script: {instanceID: 17020}
    - Class: 114
      Script: {instanceID: 17568}
    - Class: 114
      Script: {instanceID: 17622}
    - Class: 114
      Script: {instanceID: 17640}
    - Class: 114
      Script: {instanceID: 17678}
    - Class: 114
      Script: {instanceID: 17690}
    - Class: 114
      Script: {instanceID: 17702}
    - Class: 114
      Script: {instanceID: 17708}
    - Class: 114
      Script: {instanceID: 17816}
    - Class: 114
      Script: {instanceID: 17818}
    - Class: 114
      Script: {instanceID: 17876}
    - Class: 114
      Script: {instanceID: 17916}
    - Class: 114
      Script: {instanceID: 17926}
    - Class: 114
      Script: {instanceID: 18020}
    - Class: 114
      Script: {instanceID: 18094}
    - Class: 114
      Script: {instanceID: 18108}
    - Class: 114
      Script: {instanceID: 18266}
    - Class: 114
      Script: {instanceID: 18572}
    - Class: 114
      Script: {instanceID: 18716}
    - Class: 114
      Script: {instanceID: 18736}
    - Class: 114
      Script: {instanceID: 18810}
    - Class: 114
      Script: {instanceID: 18926}
    - Class: 114
      Script: {instanceID: 19006}
    - Class: 114
      Script: {instanceID: 19038}
    - Class: 114
      Script: {instanceID: 19080}
    - Class: 114
      Script: {instanceID: 19306}
    - Class: 114
      Script: {instanceID: 19380}
    - Class: 114
      Script: {instanceID: 19436}
    - Class: 114
      Script: {instanceID: 19476}
    - Class: 114
      Script: {instanceID: 19502}
    - Class: 114
      Script: {instanceID: 19702}
    - Class: 114
      Script: {instanceID: 19786}
    - Class: 114
      Script: {instanceID: 19794}
    - Class: 114
      Script: {instanceID: 19986}
    - Class: 114
      Script: {instanceID: 20092}
    - Class: 114
      Script: {instanceID: 20302}
    - Class: 114
      Script: {instanceID: 20434}
    - Class: 114
      Script: {instanceID: 20564}
    - Class: 114
      Script: {instanceID: 20616}
    - Class: 114
      Script: {instanceID: 8327992}
    - Class: 114
      Script: {instanceID: 8328000}
    - Class: 114
      Script: {instanceID: 8328002}
    - Class: 114
      Script: {instanceID: 8328004}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 119
      Script: {instanceID: 0}
    - Class: 121
      Script: {instanceID: 0}
    - Class: 123
      Script: {instanceID: 0}
    - Class: 124
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 134
      Script: {instanceID: 0}
    - Class: 153
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    Assets/Scenes/tractortochan.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 16188}
    - Class: 114
      Script: {instanceID: 16190}
    - Class: 114
      Script: {instanceID: 16192}
    - Class: 114
      Script: {instanceID: 16480}
    - Class: 114
      Script: {instanceID: 16886}
    - Class: 114
      Script: {instanceID: 16946}
    - Class: 114
      Script: {instanceID: 16966}
    - Class: 114
      Script: {instanceID: 17318}
    - Class: 114
      Script: {instanceID: 17568}
    - Class: 114
      Script: {instanceID: 17816}
    - Class: 114
      Script: {instanceID: 18094}
    - Class: 114
      Script: {instanceID: 18496}
    - Class: 114
      Script: {instanceID: 18572}
    - Class: 114
      Script: {instanceID: 18690}
    - Class: 114
      Script: {instanceID: 18760}
    - Class: 114
      Script: {instanceID: 19138}
    - Class: 114
      Script: {instanceID: 19536}
    - Class: 114
      Script: {instanceID: 19904}
    - Class: 114
      Script: {instanceID: 20372}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 121
      Script: {instanceID: 0}
    - Class: 123
      Script: {instanceID: 0}
    - Class: 124
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 146
      Script: {instanceID: 0}
    - Class: 153
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 208
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: 7bcb35908a60cac26edee0ff51816ada
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: a9d9d285d996b592f1ccf674fa017099
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 8165edc0ea71078aadd02b6c3ce945a1
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: 43cc9e552cbae588a31fd8f1fcf75d25
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLightFade
  - hash:
      serializedVersion: 2
      Hash: c1058004017c4d95e55a1c77d955e000
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelRaycaster
  - hash:
      serializedVersion: 2
      Hash: 1364684c030d5d864e68bd2364f7f2e8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Vehicles
  - hash:
      serializedVersion: 2
      Hash: 067f6ba9d6063b307f995a8e9a978584
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SMGGameManager
  - hash:
      serializedVersion: 2
      Hash: 480e2a56c81cbe122ffc7c3fe7336a07
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: e0890e75ccd62f2157ee5b6bce528699
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColliderBehaviour
  - hash:
      serializedVersion: 2
      Hash: 70c1b21b00314bcc18820019e550c423
    assemblyName: GoogleMobileAds.Editor.dll
    namespaceName: GoogleMobileAds.Editor
    className: GoogleMobileAdsSettings
  - hash:
      serializedVersion: 2
      Hash: 10a0fb7fb0f8deb3b8b4b0008125225e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CarSelectionExample
  - hash:
      serializedVersion: 2
      Hash: ab515f173cfc0d80084a065efe56a44f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Caliper
  - hash:
      serializedVersion: 2
      Hash: e4f8e992bd8551aef082a903aad96f64
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePath
  - hash:
      serializedVersion: 2
      Hash: 89b081f67b76cf30c49d88a2ca6967a7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachineVolumeSettings
  - hash:
      serializedVersion: 2
      Hash: a87e99b75dfaa0c6f8e7646e7bca3ca8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FOVForCinematicCamera
  - hash:
      serializedVersion: 2
      Hash: a106667e49fe46f6fa38808b669906cb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AutoTypeText
  - hash:
      serializedVersion: 2
      Hash: e688ed407bbfb11c21058187316ed44e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: f64cef5e050a62f416f4a5e6d49d6fe5
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PreferenceDictionary
  - hash:
      serializedVersion: 2
      Hash: 66c8285bd714a31aecffd72f9f0585da
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CreateAudioSource
  - hash:
      serializedVersion: 2
      Hash: a506b72c53f139c380ebe7f3581a434b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 10b44983f0a7578909bb4d68fa771902
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: f810935f76d28048a0a4be4479a0b8c9
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineExternalCamera
  - hash:
      serializedVersion: 2
      Hash: da05abcc878171591b25804eef377360
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 49c8a1de997b38f462023bb8d7442419
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Tractorsense
  - hash:
      serializedVersion: 2
      Hash: 57a255c0ca22daf718d55ec02240ce8b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SettingAD
  - hash:
      serializedVersion: 2
      Hash: d5dbba1e095217e88ae7faf82e4e2dbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: 3e8a67702e7670d23688421f8f8f61a8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: InAppUpdate
  - hash:
      serializedVersion: 2
      Hash: ac51533c95676e7af6ba886a0ed52d0d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFreeLook
  - hash:
      serializedVersion: 2
      Hash: 7a0b07b1892f2a5c214ff6d300f598c7
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTriggerAction
  - hash:
      serializedVersion: 2
      Hash: b46a27dc8fe542927657974694fc29aa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 87a477c997c2f91d9c0b88c63927e543
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoopScript
  - hash:
      serializedVersion: 2
      Hash: 2380288dfe403a7f4b2182b575687cb8
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 7cea3daad0f112a7159c571a80f7af7b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCCOrbitReset
  - hash:
      serializedVersion: 2
      Hash: cbbd1e2df49abced988f10276a127924
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC
  - hash:
      serializedVersion: 2
      Hash: b6919327bcca55e4e56b20b361fa8ee4
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: b34c61c6f1509ce111a79002fc10e5a3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiButtonScript
  - hash:
      serializedVersion: 2
      Hash: 66ae6dfd00d882b548b0f6848a4b8b38
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GDPRScript
  - hash:
      serializedVersion: 2
      Hash: e78f6985cfa9077afa7bb6c3ee81529f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: bded33e439631f087d3612601816819f
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineCameraOffset
  - hash:
      serializedVersion: 2
      Hash: 23a3c4121b82249c48f28f7c51cb8027
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FixedCamera
  - hash:
      serializedVersion: 2
      Hash: d95e47c7038bd218cf8ccad141e1adcf
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseAnalytics
  - hash:
      serializedVersion: 2
      Hash: 7c452fbf2d21a6ff3c4e80d869adcab1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UISliderTextReader
  - hash:
      serializedVersion: 2
      Hash: 74177df3eaaf089041860685bdb8ea15
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_PoliceSiren
  - hash:
      serializedVersion: 2
      Hash: 1b794936b7204d6c3bc107ef758de25e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CustomizerExample
  - hash:
      serializedVersion: 2
      Hash: 112bc40a456c5b0bfe768c00a7a89ade
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LevelLock
  - hash:
      serializedVersion: 2
      Hash: 700cc65c5d61eca10bcacb25a2d31a1d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GoogleMobileAdsConsentController
  - hash:
      serializedVersion: 2
      Hash: f057fa726d85211de6325e3124077201
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardInputs
  - hash:
      serializedVersion: 2
      Hash: 192bcb00de0b6dc327b080f43a033895
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollider
  - hash:
      serializedVersion: 2
      Hash: bb060e951a6bd505c931e7a54e0ce6d4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: player
  - hash:
      serializedVersion: 2
      Hash: 5d2eca6f6cfbab49b6fa5ee0d302f2f0
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UIElements
    className: PanelEventHandler
  - hash:
      serializedVersion: 2
      Hash: 1f2a952fbe7bf81aeeaf3f785705c6e8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Chassis
  - hash:
      serializedVersion: 2
      Hash: 490b8ff05ebe54ba8d7bb4553573f5af
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIController
  - hash:
      serializedVersion: 2
      Hash: eaa9973d190f4fdb98db68ee59821eeb
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Birdtriiger
  - hash:
      serializedVersion: 2
      Hash: 2be854dee95084c1537e4d6bb482ca5d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineDollyCart
  - hash:
      serializedVersion: 2
      Hash: 38e7a5b163c4394727bc53d6d2090f88
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlenderSettings
  - hash:
      serializedVersion: 2
      Hash: bfcc61e961dee25cef9c7d7ba35b4fe4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SliderRelease
  - hash:
      serializedVersion: 2
      Hash: 43b9d383cd12a2b17594e5b47f5f5644
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GameOptimizer
  - hash:
      serializedVersion: 2
      Hash: 52068f4f13f0846a083b3045cf305e9b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SkidmarksManager
  - hash:
      serializedVersion: 2
      Hash: ce29c624b4e215f90c5fc5df651c5363
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 319f66ea1036bda420351a9a4ac0fde3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: playermain
  - hash:
      serializedVersion: 2
      Hash: 9605a0475669ece0189f675c4d408e5f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 8e0c8b499f64b686c0ec50f43e94d3af
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 73db20b19a7ddd3e8a0823ce974766a1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SuspensionArm
  - hash:
      serializedVersion: 2
      Hash: cd72280b7aeae3ad3c5c84174bad3936
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LevelLockImages
  - hash:
      serializedVersion: 2
      Hash: cdd1cd2371920ac0808450139031539d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ChangableWheels
  - hash:
      serializedVersion: 2
      Hash: baf218a9799098962f3ddb5d79364282
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: fbc58186bcba82f31a01311018e75a24
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RECtangleAD
  - hash:
      serializedVersion: 2
      Hash: 5b62a6e927b562a7958dde4cd7cae6a1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointWalker
  - hash:
      serializedVersion: 2
      Hash: b8c62e6fbbd49b0adf47ca1dfd4b7754
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 006e9a6d9526e39e04ab3b71ae3ac7b8
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine.PostFX
    className: CinemachinePostProcessing
  - hash:
      serializedVersion: 2
      Hash: 1544cedbd70586f52825fae58f995a6d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineOrbitalTransposer
  - hash:
      serializedVersion: 2
      Hash: f2cf443dfe4f7bb39cbe44eaca3c5086
    assemblyName: DemiLib.dll
    namespaceName: DG.DemiLib.External
    className: DeHierarchyComponent
  - hash:
      serializedVersion: 2
      Hash: 70f2d4180e0c617c181af9225d783eb4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: starFxControllerMy
  - hash:
      serializedVersion: 2
      Hash: 5a17edc0c9cded335dbde56ff6c83b5d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: brake
  - hash:
      serializedVersion: 2
      Hash: d4352cb5583b70809429a4d1ea810f24
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CharacterController
  - hash:
      serializedVersion: 2
      Hash: e2477e44c810d3b250244b544b47146f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_XRToggle
  - hash:
      serializedVersion: 2
      Hash: eb17a75a770e21172667a14ff95a2247
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PauseAd
  - hash:
      serializedVersion: 2
      Hash: d78158674167b3da1fad56b0208ae288
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBrain
  - hash:
      serializedVersion: 2
      Hash: 1a954480ab8a39b6fb0a62f6d1592ba3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoadSceneOnClick
  - hash:
      serializedVersion: 2
      Hash: 940f185a7114db76938f52f483172434
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ObjectRangeManager
  - hash:
      serializedVersion: 2
      Hash: e8864c11d09292c85014396a9a6526af
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SenseTraffic
  - hash:
      serializedVersion: 2
      Hash: e060f19d4f072a9672e7cdac8b10cbf1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: vfxController
  - hash:
      serializedVersion: 2
      Hash: 15e2174438103ba23087ee5cc6c0e351
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner
  - hash:
      serializedVersion: 2
      Hash: 0e321e09912fc665b76788dc31b0abfe
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSameAsFollowTarget
  - hash:
      serializedVersion: 2
      Hash: 5aa4fc9c586bfc1f7d56918fab9e667e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineConfiner2D
  - hash:
      serializedVersion: 2
      Hash: d7acbd6ad1b391d94a914128f69d9325
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineVirtualCamera
  - hash:
      serializedVersion: 2
      Hash: 72a11fb010453981333c87f2fd79962c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: IgnoreCollisions
  - hash:
      serializedVersion: 2
      Hash: d13609b6d89635cbc29df61da760371b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineClearShot
  - hash:
      serializedVersion: 2
      Hash: 0ab78f031b53dd5d55f964f3cd1c223e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: 80514a4aad8eb11350a6a1aa6025b6d4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WC15PRO
  - hash:
      serializedVersion: 2
      Hash: ef2a760dc8c0bb333363120518871762
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: fe9dd235f0d12e79744be18347cb632a
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiLoadSceneOnClick2
  - hash:
      serializedVersion: 2
      Hash: 46f8815aaefa696d509049c348ad6805
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: c3220f7afd647b11dae09800190b3898
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: a152f4bbfcc1c203222264e9469e6cd6
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: cdeabb5f77f9ec8f804242ddf7c2e306
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: PluginManager
  - hash:
      serializedVersion: 2
      Hash: 703ddfbc436d7aee1117bc2332a27e8f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SciFiLightFlicker
  - hash:
      serializedVersion: 2
      Hash: f659bfd5a895d98ef879c86a3c7d473b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_TruckTrailer
  - hash:
      serializedVersion: 2
      Hash: ed034cff83e1d608e66691a37102630c
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 448673bdf08983813306c834f27377f3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ShadowRotConst
  - hash:
      serializedVersion: 2
      Hash: b987e7aa6d5cdf5270023c38f0d8e63e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIO
  - hash:
      serializedVersion: 2
      Hash: ede167ceff9e5f1ce8b59212dcb0e526
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_MobileUIDrag
  - hash:
      serializedVersion: 2
      Hash: 6e93352154a8edfd62ff0a0bea9c005e
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder.Shapes
    className: ProBuilderShape
  - hash:
      serializedVersion: 2
      Hash: 3d02ff497fa903158a32fa89adf3810b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePipeline
  - hash:
      serializedVersion: 2
      Hash: 4bcac0545b8ecc4aa77d06b222ce018e
    assemblyName: Assembly-CSharp.dll
    namespaceName: VisCircle
    className: PowerUpAnimation
  - hash:
      serializedVersion: 2
      Hash: a5e2bf54433abebd77cadf6fd7975d16
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LivingOcean
  - hash:
      serializedVersion: 2
      Hash: c1d199d576a45011fe74dcdca7001260
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_WheelCollider
  - hash:
      serializedVersion: 2
      Hash: bb625ca5427f419e16ddc7db09bca863
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_InfoLabel
  - hash:
      serializedVersion: 2
      Hash: 5036a44ed8fda752a85add5acc8996f6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_HoodCamera
  - hash:
      serializedVersion: 2
      Hash: c895614a6ad484093b565c53e1aca8ba
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardColors
  - hash:
      serializedVersion: 2
      Hash: fd5618094d1ec353bc50abe24baaba92
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLookAt
  - hash:
      serializedVersion: 2
      Hash: 68b4edb102499623f0792d864422fda2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AdmobManager
  - hash:
      serializedVersion: 2
      Hash: 83ab2eee401291ff0aeaae7adfa8dcb4
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColorPalette
  - hash:
      serializedVersion: 2
      Hash: 84c295144a2fce36bf4afcb7fb81265f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: df6eab954ec864d23f72bf3587a64b76
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: c457864c18c9ac0b151a723e4fae4ea4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: BannerAD
  - hash:
      serializedVersion: 2
      Hash: e334ba8ceb0eb18fbce4c1d9f35143df
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: InAppReview
  - hash:
      serializedVersion: 2
      Hash: 9e8898d1705d409ec4c988108072c8af
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 68d5dec1c60c68c2dfc1f02723e0b735
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: bcb9b7a93c09c207eb313c02affe0d60
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 56484752efc49aaf5720e7c166075e4d
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: Entity
  - hash:
      serializedVersion: 2
      Hash: aa325108ef128d3a378c0209007e2b82
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_MobileButtons
  - hash:
      serializedVersion: 2
      Hash: 8c5ab079dbc06dd660a4f3f0f25c099e
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: 21cafa55c36e9705ae45286be79aef87
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Records
  - hash:
      serializedVersion: 2
      Hash: aa2b85e5e7db57befd8f023e31b2e2b5
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineInputProvider
  - hash:
      serializedVersion: 2
      Hash: cf439fe8eb6bdf602e473453f4c73e36
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC
  - hash:
      serializedVersion: 2
      Hash: ed2094e12f032ec673dcde8d41537bd5
    assemblyName: GoogleMobileAds.Common.dll
    namespaceName: GoogleMobileAds.Common
    className: AppStateEventClient
  - hash:
      serializedVersion: 2
      Hash: df356894f021f8fa9b65818185a6de20
    assemblyName: GoogleMobileAds.Common.dll
    namespaceName: GoogleMobileAds.Common
    className: MobileAdsEventExecutor
  - hash:
      serializedVersion: 2
      Hash: d2ab0b299147f65834573ffd942ba546
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTrack
  - hash:
      serializedVersion: 2
      Hash: 60b7323b901b245b1145ca70bb973071
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIBrakeZone
  - hash:
      serializedVersion: 2
      Hash: 18489aba93650f0e6326bf36f0c1b922
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonFollow
  - hash:
      serializedVersion: 2
      Hash: 551fc332ca1d34a147749dd8966d5bc4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LightAnimation
  - hash:
      serializedVersion: 2
      Hash: 4ace94bcd82a8892b2c26e2635218fa1
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseAnalyticsHandler
  - hash:
      serializedVersion: 2
      Hash: 715601b57e10295e9be59913b14b0dbe
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_GroundMaterials
  - hash:
      serializedVersion: 2
      Hash: 0fcf558c0107682d62bba219945bb20d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AICarController
  - hash:
      serializedVersion: 2
      Hash: 220e801681412245aece5c38add147b8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SceneLoad
  - hash:
      serializedVersion: 2
      Hash: 9690f760294698c29b1bee7d9b7bd452
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: a273edb1f10a7eafb870721bc4bddaca
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 0ffacc03a651624ad5bb72bc787275f7
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: ea10b700f0d223aab296caf9b84ea302
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: BezierShape
  - hash:
      serializedVersion: 2
      Hash: 6a92b2e660dc4d1eda27032865cbf385
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Spawner
  - hash:
      serializedVersion: 2
      Hash: ceb4961d39f347996fda1404ac174d05
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: cde977fb11d1f6ae6783d1b69745d58a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseSource
  - hash:
      serializedVersion: 2
      Hash: 6691dd18200d133e8dbae24b6281d1cf
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: 39921e4b272e731c762bfa7230c3b636
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GameAnalyticsInitializer
  - hash:
      serializedVersion: 2
      Hash: dce5b66613be781b5459af45db50e03b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RewardReadyCheck
  - hash:
      serializedVersion: 2
      Hash: 4a3351e209ecf0eff6ab268e49d7f341
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: d3b117887d664b796e08789f8a61806f
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineMixingCamera
  - hash:
      serializedVersion: 2
      Hash: c1933688180ee845ee0ae3bc5e3971e6
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineImpulseListener
  - hash:
      serializedVersion: 2
      Hash: 52f7ee253aba09630d5f15e7fbc84038
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MintegralRoas
  - hash:
      serializedVersion: 2
      Hash: 9fc14b809d7da3f0438dad852aaea6b0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WeatherSystem
  - hash:
      serializedVersion: 2
      Hash: 4828c433443bed8b1bb283af97d6e196
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Skidmarks
  - hash:
      serializedVersion: 2
      Hash: c722b438a375a028ec8b5c5f8c33cbce
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenPath
  - hash:
      serializedVersion: 2
      Hash: 5e8d44b28cc321823122fb9b9f6cf30e
    assemblyName: DOTweenPro.dll
    namespaceName: DG.Tweening
    className: DOTweenVisualManager
  - hash:
      serializedVersion: 2
      Hash: 5b73dfa7ead37d059de3031423565849
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Rotator
  - hash:
      serializedVersion: 2
      Hash: 33351225ad07d388c6f33d76ccfe9ba6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: c1f3d85aacdd221eebc8efdca44b1e74
    assemblyName: Firebase.Platform.dll
    namespaceName: Firebase.Platform
    className: FirebaseMonoBehaviour
  - hash:
      serializedVersion: 2
      Hash: e4c58e3b6491d44e8cd0a7a86c4b7014
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: TriggerBehaviour
  - hash:
      serializedVersion: 2
      Hash: 17df78ad7cc29bd75f94172342133611
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WeatherManager
  - hash:
      serializedVersion: 2
      Hash: 42f3d612fa6b74e88ddb99569024244d
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiRotation
  - hash:
      serializedVersion: 2
      Hash: 50096ec6de83fcf81331535bbc8e7157
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DistanceFromPlayer
  - hash:
      serializedVersion: 2
      Hash: a778f0b63dcf4d9df48972789d1e49a6
    assemblyName: Assembly-CSharp-firstpass.dll
    namespaceName: DG.Tweening
    className: DOTweenAnimation
  - hash:
      serializedVersion: 2
      Hash: 68587ea39bb503ea8d65053e93c31d06
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: dbf1dfa253e20667829f0817c5b6d684
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineSmoothPath
  - hash:
      serializedVersion: 2
      Hash: 2fbd6c28e1a03872d56abf53df14d753
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Animationtexture
  - hash:
      serializedVersion: 2
      Hash: 60dac5e8b3c87c7e809d4947f05f2a83
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIBrakeZonesContainer
  - hash:
      serializedVersion: 2
      Hash: dda852df7868289b03e5e09293f0c48a
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiProjectileScript
  - hash:
      serializedVersion: 2
      Hash: c775193bec472a91767f76dccfe603e7
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: d12a5d3cc62a1922e8894604591471af
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ButtonChildToggle
  - hash:
      serializedVersion: 2
      Hash: ebd0409eb7f02c30d237d55591f7b5a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePixelPerfect
  - hash:
      serializedVersion: 2
      Hash: c12745607fab0e74d57fbfebcb8ee7ac
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Recorder
  - hash:
      serializedVersion: 2
      Hash: 5dd80437a0058459586b1a3f7117ab60
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MeshCombiner
  - hash:
      serializedVersion: 2
      Hash: 678e1b1d2ead89a22d454421d06258e4
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 60848472e362199ae5eac55c8892dcbe
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: bd4016a2045f75e945497f07c5b80503
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: 693a704080e97409a6aad060aa1be540
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: wheelrotator
  - hash:
      serializedVersion: 2
      Hash: 51f10e7961758dd62a1ec2f7863be045
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenComponent
  - hash:
      serializedVersion: 2
      Hash: bf75fa8cb4f20aa58bc51646b041fb2b
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenSettings
  - hash:
      serializedVersion: 2
      Hash: f87a82bdc1582c5f70b70ca85233d7f2
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 3d852ed0e8c9b38616d90b775f92a20e
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFixedSignal
  - hash:
      serializedVersion: 2
      Hash: 32ef7e612a70655f7ed0cd30d96806e2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Light
  - hash:
      serializedVersion: 2
      Hash: d301a4a7f75108c30e3d5c25aceea2d8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: COMPAD
  - hash:
      serializedVersion: 2
      Hash: 535f226b42413a750ae27d52859dc953
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MainMenu
  - hash:
      serializedVersion: 2
      Hash: 4fe30b079bc8419c58089caa901d9184
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFramingTransposer
  - hash:
      serializedVersion: 2
      Hash: ba669a82de932d6eb54004566e0bb9b6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FireBaseInitializer
  - hash:
      serializedVersion: 2
      Hash: 111d589e9cb42750fdacf7d6d13c61a5
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: NoiseSettings
  - hash:
      serializedVersion: 2
      Hash: acbb5b301203f6c0e9a4e0429d9f93dc
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Useless
  - hash:
      serializedVersion: 2
      Hash: 466329aa1430edd5d9cc35262ee17096
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: b309f0b8b517f50a2601fbb7a2f9f705
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: f9786a3dca6228a6818ec4a0134d9212
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: 769abea9aa79f49a2ee1a4facd0a61d5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: 805c62fb3df38a31dfa854c4f1d54dfa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: c05e73c4535140d8b52eabb410ae089c
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 2241b75a4870f4d03fb96e40e114c746
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UISteeringWheelController
  - hash:
      serializedVersion: 2
      Hash: d71d9c16bdf050b8c33e1f3664cf8d1b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: mode2lock
  - hash:
      serializedVersion: 2
      Hash: 421bffbee2ac47b81d60c69e2a3d5ac7
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: cronspray
  - hash:
      serializedVersion: 2
      Hash: 459334ca57e17e388588a5fb8a5d4c44
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTransposer
  - hash:
      serializedVersion: 2
      Hash: dac1033fcebddf6e553faf4cd30cc51a
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ScrollUV
  - hash:
      serializedVersion: 2
      Hash: 9fa5630af1eae1eddfb3d63d5fc08f3f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DontDestroyOnLoad
  - hash:
      serializedVersion: 2
      Hash: 03537dd42b6fc73a601799c77ceb0b9c
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: 453c286dc9c9c693626ccfeab876ea44
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 0bd403cafe5bfb0c5bbda285dc190598
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineShot
  - hash:
      serializedVersion: 2
      Hash: 9a81ed6e856550f8086a6614cb8184ae
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CinematicCamera
  - hash:
      serializedVersion: 2
      Hash: 9804bf9b4e94d9e0e5a4fb43422b2d07
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC_Waypoint
  - hash:
      serializedVersion: 2
      Hash: 74101b1189e7f911aef3b5ca95b2db19
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 551e99ad30b702f2eb620c73d347b985
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WPC_Editor
  - hash:
      serializedVersion: 2
      Hash: 933cf14e162bdaa406cbe03b3457a52d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LoadingAD
  - hash:
      serializedVersion: 2
      Hash: 32e43b20e01fab16051102e3b3053943
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_FuelStation
  - hash:
      serializedVersion: 2
      Hash: 3c3e5980c88a14a86741564a7fd08aaa
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 614c1a43e0f3bbf140358b35cf9aa13a
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineIndependentImpulseListener
  - hash:
      serializedVersion: 2
      Hash: 9fec09927a72e8b48d04a59cd56a880c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: starFxController
  - hash:
      serializedVersion: 2
      Hash: f9ae240b9b17ce4bb73a247498c010d1
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PolyShape
  - hash:
      serializedVersion: 2
      Hash: 55ac714ecd14f17465c0e21a680e5b69
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: d3669877414759386a730a648c50f255
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTrackedDolly
  - hash:
      serializedVersion: 2
      Hash: 24d0049bc4be6ae105eb4e99236b80e8
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CameraCarSelection
  - hash:
      serializedVersion: 2
      Hash: 238ea53fa78c4727fec017c6481aaca0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: LoadingLogic
  - hash:
      serializedVersion: 2
      Hash: 2bd783b5b85429caff317fd8ece19300
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 15f833455ba334a3b6ca548b31eac8f6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIDashboardButton
  - hash:
      serializedVersion: 2
      Hash: 81edb5e973a40cf9ff08f4c26f68a9a0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineGroupComposer
  - hash:
      serializedVersion: 2
      Hash: 281111590cca9ff7b2b5cbc973d0ba1a
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: DemoController
  - hash:
      serializedVersion: 2
      Hash: 5102ffa1370d4d486db510e3c2fc3e83
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_ColorPickerBySliders
  - hash:
      serializedVersion: 2
      Hash: 1ff76b7a82b2b0fd6d84eed67370a649
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: fbd3135687c248dda5118b8516726e6c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_TrailerAttachPoint
  - hash:
      serializedVersion: 2
      Hash: c868a8242d388de5431d73295daeac2d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Settings
  - hash:
      serializedVersion: 2
      Hash: f612f6956389e86994c43050be53d1d0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_APIExample
  - hash:
      serializedVersion: 2
      Hash: 21f4f1fec12583058b4c5843abeefdbb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 6a9516770b070951680a95390b9098a5
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: a1926869a1b71ceb204692733af622c6
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: 4a0457e2e8e1314798dc96306d452939
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: wheelAi
  - hash:
      serializedVersion: 2
      Hash: 6fea02e0a8b3cb3b15440b3ff3fce29b
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 8088a6e6266ae3e4922a5d6c505f6a7e
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineTouchInputMapper
  - hash:
      serializedVersion: 2
      Hash: 74d10f1ce9336a3e675e041537da79e0
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStoryboard
  - hash:
      serializedVersion: 2
      Hash: b8035b84df1d28a4c0ee0b0a41440520
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: MaxMediation
  - hash:
      serializedVersion: 2
      Hash: 621258cbf131e999213abbde65c03985
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 99e345688e823804d80e19d868be9f49
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_WheelCamera
  - hash:
      serializedVersion: 2
      Hash: dc3d5271f3271de34751f74fb838e919
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: Cinemachine3rdPersonAim
  - hash:
      serializedVersion: 2
      Hash: 03116c959005bf565efe4a31f8be265e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: 4ea7de30cb668cc5a4a70a803205db1d
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineStateDrivenCamera
  - hash:
      serializedVersion: 2
      Hash: 9b20bb37fe547a38e67e865c7074ed8f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: b2f38a35d6d6d225625e7c825760210f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Testingads
  - hash:
      serializedVersion: 2
      Hash: 3d58a5fe1892e5295f1cf87c032ae18c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointsHolder
  - hash:
      serializedVersion: 2
      Hash: e9bc04f439c5f185c9ebd236fa9d990b
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiDragMouseOrbit
  - hash:
      serializedVersion: 2
      Hash: 708832204db491ea52088816ccdd8a92
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIDashboardDisplay
  - hash:
      serializedVersion: 2
      Hash: b187c202ddcafd4c52781696e213aad2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineHardLockToTarget
  - hash:
      serializedVersion: 2
      Hash: 8bd0ca6d540f998ba01649a3559a391d
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_AIWaypointsContainer
  - hash:
      serializedVersion: 2
      Hash: 1d1e664e0d8337c7cb2af559a14457c6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Customization
  - hash:
      serializedVersion: 2
      Hash: b51c2cb4416f46fd59809ff697c7b6d3
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AlertMessage
  - hash:
      serializedVersion: 2
      Hash: 8e4f99c1f9019b681acd5188f5fcfeb4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_UIJoystick
  - hash:
      serializedVersion: 2
      Hash: 5d77ddbaa8a9ea3eb8abf85398c27d56
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: GroupWeightManipulator
  - hash:
      serializedVersion: 2
      Hash: a79acf0718e5154ccc422af5f1465ae2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_LightEmission
  - hash:
      serializedVersion: 2
      Hash: f80ec5faf2372bddd6907d63028ba945
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: TimerSeconds
  - hash:
      serializedVersion: 2
      Hash: 83f39d6aef5423b963dbd7666758231b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Waypoint
  - hash:
      serializedVersion: 2
      Hash: 1808bedf05234f59aad67504dc589413
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineTargetGroup
  - hash:
      serializedVersion: 2
      Hash: 9ed9e421d17369f1952e18cc1edadbcb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: cbdd15c00e527b842c7e8874f25cbfd9
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WaypointMover
  - hash:
      serializedVersion: 2
      Hash: 5f14f78bc41036bae00594751d994b33
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_LevelLoader
  - hash:
      serializedVersion: 2
      Hash: 3acbd07fe034e5e58071c1e2d114ede4
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: IAP_Controller
  - hash:
      serializedVersion: 2
      Hash: ddb4481606a34c99382b812c001817a2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Fail
  - hash:
      serializedVersion: 2
      Hash: c8029497208110bd26cfc6e8e10e4aa6
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_GetBounds
  - hash:
      serializedVersion: 2
      Hash: d8b0ea311ade16e0ef339de287a68d43
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Exhaust
  - hash:
      serializedVersion: 2
      Hash: 7219ebb0b4a64fd19236e423b34046f6
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: c3409785540d7aefc3e15014e19268a3
    assemblyName: Assembly-CSharp.dll
    namespaceName: SciFiArsenal
    className: SciFiFireProjectile
  - hash:
      serializedVersion: 2
      Hash: cd5e4b671a6f3e47fe319d5913a5513e
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: cc6ba68260ea5e6304249d5ea33f06e5
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SaveButtonController
  - hash:
      serializedVersion: 2
      Hash: cb6e436fad8ad205794db196b22d97a2
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineCollisionImpulseSource
  - hash:
      serializedVersion: 2
      Hash: cbe87c73286ec8440014472d11def03b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Camera
  - hash:
      serializedVersion: 2
      Hash: 8e35fe2655309f23b1e0b22231b6ab57
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachinePOV
  - hash:
      serializedVersion: 2
      Hash: 265c0392a7bafc0e300e5c5f6c3f9aad
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_DashboardObjects
  - hash:
      serializedVersion: 2
      Hash: a49f31917d44a804be83a7e958ff7709
    assemblyName: Cinemachine.dll
    namespaceName: 
    className: CinemachineRecomposer
  - hash:
      serializedVersion: 2
      Hash: 31d0d66962989f5f311b2a1d2ed736c4
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBasicMultiChannelPerlin
  - hash:
      serializedVersion: 2
      Hash: cda94de9b147df6d528100b4951eff3f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: targetcam
  - hash:
      serializedVersion: 2
      Hash: 594a9bf252b579ddcda1f1ab94e15608
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AdsController
  - hash:
      serializedVersion: 2
      Hash: 82aafca91c0718ffcf931c70fdd09fcf
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: FirebaseRemoteConfigHandler
  - hash:
      serializedVersion: 2
      Hash: 9dc87b1d1b125f7d5b1cf7e2e9732e5c
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: UnityAdsManager
  - hash:
      serializedVersion: 2
      Hash: 020ed23a35cf951715dfcfd17f8abe34
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_SceneManager
  - hash:
      serializedVersion: 2
      Hash: af3c7bb5f1a72685c8bff2aeb7ca8d1b
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 087270dae29f8e16c4d04b8a5007b275
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: manger2
  - hash:
      serializedVersion: 2
      Hash: 0d2df17825c95a1982d89a0742af89a0
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Demo
  - hash:
      serializedVersion: 2
      Hash: 999c449ad95117c75b6263e062d2e58e
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b9484dc0a83a5260f8b27b6c3efe7f6b
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineComposer
  - hash:
      serializedVersion: 2
      Hash: 5a027759c00a61ba61a56a66e14e887e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SciFiBeamScript
  - hash:
      serializedVersion: 2
      Hash: d7b947190b978e5a6fdf2073ec7aed53
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: AttPermissionRequest
  - hash:
      serializedVersion: 2
      Hash: 9f8efa23f04cda3535b08edc0ec458a0
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ProBuilderMesh
  - hash:
      serializedVersion: 2
      Hash: 78fa8c70bef8c6d96ce76e6d539a1987
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 75f58323157f9b4e13d2528e58640a72
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineFollowZoom
  - hash:
      serializedVersion: 2
      Hash: 829a3f3825b2b9d72365ef92d8177711
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: 056e06b22d42feb19ff3807a35d6efa1
    assemblyName: Cinemachine.dll
    namespaceName: Cinemachine
    className: CinemachineBlendListCamera
  - hash:
      serializedVersion: 2
      Hash: 0528873fc974e4c59f837d1d4ecf0115
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: ImageArrayDisplay
  - hash:
      serializedVersion: 2
      Hash: 01584e5b6d82fd214425fbc0450fff17
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: startscene
  - hash:
      serializedVersion: 2
      Hash: 2eedad2de7a2cdac7488349860a0dae2
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_Mirror
  - hash:
      serializedVersion: 2
      Hash: 6b503bab89add6314dd50ac85be6b433
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: RCC_CarControllerV3
  - hash:
      serializedVersion: 2
      Hash: 4399236925123c492081e93db6a8606b
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: WalkerCollisionHandler
  platform: 13
  scenePathNames:
  - Assets/Scenes/spleshscene.unity
  - Assets/Scenes/MAINMENU.unity
  - Assets/Scenes/gameplay.unity
  - Assets/Scenes/tractortochan.unity
  playerPath: D:/My Project/Tractor Simulator Cargo Games (V1.9)smg/Tractor Simulator
    Cargo/Apk/Tractor simulator 3.apk
