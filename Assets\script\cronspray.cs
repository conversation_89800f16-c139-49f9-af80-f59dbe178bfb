using System.Collections;
using UnityEngine;
using UnityEngine.UI;

public class cronspray : MonoBehaviour
{
    public GameObject effect, Skipbutton, completepannel, rccpannel;
    public player Manager;
    public Image FilImage;
    public Text count;
    public float targetYScale = 2.5f;
    private void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Cron"))
        {
            other.gameObject.GetComponent<BoxCollider>().enabled = false;
            StartCoroutine(SmoothTransition(other.gameObject));
        }
    }
    private IEnumerator SmoothTransition(GameObject cron)
    {
        Vector3 originalScale = cron.transform.localScale;
        float duration = 1.0f;
        float elapsedTime = 0f;
        while (elapsedTime < duration)
        {
            float t = elapsedTime / duration;

            cron.transform.localScale = new Vector3(
                originalScale.x,
                Mathf.Lerp(originalScale.y, targetYScale, t),
                originalScale.z
            );

            elapsedTime += Time.deltaTime;
            yield return null;
        }
        cron.transform.localScale = new Vector3(originalScale.x, targetYScale, originalScale.z);
        effect.SetActive(true);
        FilImage.fillAmount += 0.01f;
        count.text = FilImage.fillAmount.ToString("F2");
        if (FilImage.fillAmount >= 1f)
        {
            StartCoroutine(Manager.end());
        }
        else if (FilImage.fillAmount >= 0.3f)
        {
            Skipbutton.SetActive(true);
        }


    }
    public void Skip()
    {
        // Show rewarded interstitial ad first, then execute skip functionality in the callback
        AdsController.Instance.ShowRewardedInterstitialAd_Admob(OnAdCompleted);
    }

    private void OnAdCompleted()
    {
        // Execute the skip functionality after the ad is completed
        rccpannel.SetActive(false);
        completepannel.SetActive(true);
        AudioListener.volume = 0f;
    }
}
