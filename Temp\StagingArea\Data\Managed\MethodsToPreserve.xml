<linker>
	<assembly fullname="Assembly-CSharp" ignoreIfMissing="1">
		<type fullname="cronspray">
			<method name="Skip"/>
		</type>
		<type fullname="MainMenu">
			<method name="Btns"/>
			<method name="caremode"/>
			<method name="exit"/>
			<method name="fill"/>
			<method name="filldec"/>
			<method name="fl"/>
			<method name="fldec"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Levels"/>
			<method name="Lvel"/>
			<method name="Lvel"/>
			<method name="Lvel"/>
			<method name="Lvel"/>
			<method name="Lvel"/>
			<method name="Lvel"/>
			<method name="lvl2back"/>
			<method name="lvlback"/>
			<method name="mode2"/>
			<method name="modeback"/>
			<method name="moregams"/>
			<method name="moregams"/>
			<method name="No"/>
			<method name="play"/>
			<method name="privacy"/>
			<method name="rateus"/>
			<method name="save"/>
			<method name="select"/>
			<method name="setting"/>
			<method name="Steer"/>
			<method name="Tilt"/>
			<method name="trainadd"/>
			<method name="yes"/>
		</type>
		<type fullname="manger2">
			<method name="home"/>
			<method name="home"/>
			<method name="home"/>
			<method name="next"/>
			<method name="pause"/>
			<method name="pause"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="resume"/>
		</type>
		<type fullname="RCC_CustomizerExample">
			<method name="ChangeWheelsBySlider"/>
			<method name="SetABS"/>
			<method name="SetClutchMarginByToggle"/>
			<method name="SetClutchThresholdBySlider"/>
			<method name="SetCounterSteeringByToggle"/>
			<method name="SetESP"/>
			<method name="SetExhaustFlameByToggle"/>
			<method name="SetFrontCambersBySlider"/>
			<method name="SetFrontSuspensionDistancesBySlider"/>
			<method name="SetFrontSuspensionsSpringDamperBySlider"/>
			<method name="SetFrontSuspensionsSpringForceBySlider"/>
			<method name="SetGearShiftingThresholdBySlider"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetHeadlightColorByColorPicker"/>
			<method name="SetNOSByToggle"/>
			<method name="SetRearCambersBySlider"/>
			<method name="SetRearSuspensionDistancesBySlider"/>
			<method name="SetRearSuspensionsSpringDamperBySlider"/>
			<method name="SetRearSuspensionsSpringForceBySlider"/>
			<method name="SetRevLimiterByToggle"/>
			<method name="SetSH"/>
			<method name="SetSHStrength"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetSmokeColorByColorPicker"/>
			<method name="SetTCS"/>
			<method name="SetTransmission"/>
			<method name="SetTurboByToggle"/>
			<method name="TogglePreviewExhaustFlameByToggle"/>
			<method name="TogglePreviewSmokeByToggle"/>
		</type>
		<type fullname="RCC_UIDashboardButton">
			<method name="GearDrive"/>
			<method name="Gearreverse"/>
		</type>
		<type fullname="SaveButtonController">
			<method name="Save"/>
		</type>
		<type fullname="SMGGameManager">
			<method name="Btns"/>
			<method name="changcontrol"/>
			<method name="home"/>
			<method name="home"/>
			<method name="next"/>
			<method name="ok"/>
			<method name="pause"/>
			<method name="PlayMusic"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="restart"/>
			<method name="resume"/>
			<method name="Steer"/>
			<method name="StopMusic"/>
			<method name="Tilt"/>
		</type>
		<type fullname="startscene">
			<method name="accept"/>
			<method name="privacy"/>
		</type>
		<type fullname="WeatherSystem">
			<method name="Ad"/>
			<method name="Ad"/>
			<method name="Ad"/>
			<method name="WeatherAir"/>
			<method name="WeatherRain"/>
			<method name="Weathersunny"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.AudioModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.AudioSource">
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Play"/>
			<method name="Stop"/>
		</type>
	</assembly>
	<assembly fullname="UnityEngine.CoreModule" ignoreIfMissing="1">
		<type fullname="UnityEngine.GameObject">
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
			<method name="SetActive"/>
		</type>
	</assembly>
</linker>
