1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.smg.tractor.trolly.games.farming.game"
4    android:installLocation="preferExternal"
5    android:versionCode="24"
6    android:versionName="2.4" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
9-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
10        android:targetSdkVersion="36" />
10-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-163
13        android:anyDensity="true"
13-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:135-160
14        android:largeScreens="true"
14-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:78-105
15        android:normalScreens="true"
15-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:49-77
16        android:smallScreens="true"
16-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:21-48
17        android:xlargeScreens="true" />
17-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:106-134
18
19    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
19-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-79
19-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:22-76
20    <uses-permission android:name="android.permission.INTERNET" />
20-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-67
20-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:22-64
21
22    <uses-feature android:glEsVersion="0x00020000" />
22-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:5-54
22-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:19-51
23    <uses-feature
23-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:5-14:36
24        android:name="android.hardware.sensor.accelerometer"
24-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-61
25        android:required="false" />
25-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-33
26    <uses-feature
26-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-17:36
27        android:name="android.hardware.touchscreen"
27-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:9-52
28        android:required="false" />
28-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-33
29    <uses-feature
29-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:5-20:36
30        android:name="android.hardware.touchscreen.multitouch"
30-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-63
31        android:required="false" />
31-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:9-33
32    <uses-feature
32-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:5-23:36
33        android:name="android.hardware.touchscreen.multitouch.distinct"
33-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-72
34        android:required="false" />
34-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:9-33
35
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:5-79
36-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:25:22-76
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:5-82
37-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:27:22-79
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:5-88
38-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:28:22-85
39    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
39-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:5-83
39-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:29:22-80
40    <queries>
40-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:35:5-68:15
41
42        <!-- For browser content -->
43        <intent>
43-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:38:9-44:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
44-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
45
46            <category android:name="android.intent.category.BROWSABLE" />
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:13-74
46-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:41:23-71
47
48            <data android:scheme="https" />
48-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
48-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
49        </intent>
50        <!-- End of browser content -->
51        <!-- For CustomTabsService -->
52        <intent>
52-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:47:9-49:18
53            <action android:name="android.support.customtabs.action.CustomTabsService" />
53-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:13-90
53-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:48:21-87
54        </intent>
55        <!-- End of CustomTabsService -->
56        <!-- For MRAID capabilities -->
57        <intent>
57-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:52:9-56:18
58            <action android:name="android.intent.action.INSERT" />
58-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:13-67
58-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:53:21-64
59
60            <data android:mimeType="vnd.android.cursor.dir/event" />
60-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
61        </intent>
62        <intent>
62-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:57:9-61:18
63            <action android:name="android.intent.action.VIEW" />
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:13-65
63-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:39:21-62
64
65            <data android:scheme="sms" />
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
65-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:19-41
66        </intent>
67        <intent>
67-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:62:9-66:18
68            <action android:name="android.intent.action.DIAL" />
68-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:13-65
68-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:63:21-62
69
70            <data android:path="tel:" />
70-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:43:13-44
71        </intent>
72        <!-- End of MRAID capabilities -->
73    </queries>
74
75    <uses-permission android:name="android.permission.WAKE_LOCK" />
75-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:5-68
75-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:24:22-65
76    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
76-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:5-110
76-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:26:22-107
77    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
77-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
78
79    <application
79-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-83
80        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
80-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\800f0ebdbf82eee61967fbab7276b7a0\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
81        android:extractNativeLibs="true"
81-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:18-50
82        android:icon="@mipmap/app_icon"
82-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-80
83        android:label="@string/app_name" >
83-->D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:16-48
84        <activity
84-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:26:9-47:20
85            android:name="com.unity3d.player.UnityPlayerActivity"
85-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:13-66
86            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
86-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:28:13-194
87            android:exported="true"
87-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:13-36
88            android:hardwareAccelerated="false"
88-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:13-48
89            android:launchMode="singleTask"
89-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:13-44
90            android:resizeableActivity="false"
90-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:13-47
91            android:screenOrientation="userLandscape"
91-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-54
92            android:theme="@style/UnityThemeSelector" >
92-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:13-54
93            <intent-filter>
93-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-39:29
94                <action android:name="android.intent.action.MAIN" />
94-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:17-69
94-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:25-66
95
96                <category android:name="android.intent.category.LAUNCHER" />
96-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:17-77
96-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:27-74
97            </intent-filter>
98
99            <meta-data
99-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:13-43:40
100                android:name="unityplayer.UnityActivity"
100-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:42:17-57
101                android:value="true" />
101-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:17-37
102            <meta-data
102-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:44:13-46:40
103                android:name="android.notch_support"
103-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:45:17-53
104                android:value="true" />
104-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:17-37
105        </activity>
106
107        <meta-data
107-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:9-51:33
108            android:name="unity.splash-mode"
108-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:13-45
109            android:value="2" />
109-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:13-30
110        <meta-data
110-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:52:9-54:36
111            android:name="unity.splash-enable"
111-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:53:13-47
112            android:value="True" />
112-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:13-33
113        <meta-data
113-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:9-57:36
114            android:name="unity.launch-fullscreen"
114-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-51
115            android:value="True" />
115-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:13-33
116        <meta-data
116-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:58:9-60:50
117            android:name="notch.config"
117-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:13-40
118            android:value="portrait|landscape" />
118-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:13-47
119        <meta-data
119-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:9-63:36
120            android:name="unity.auto-report-fully-drawn"
120-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:62:13-57
121            android:value="true" />
121-->[:unityLibrary] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:63:13-33
122
123        <activity
123-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
124            android:name="com.google.android.gms.common.api.GoogleApiActivity"
124-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:20:19-85
125            android:exported="false"
125-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:22:19-43
126            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
126-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1b2a8be1f7c20051c0ee2c7353491a0\transformed\jetified-play-services-base-18.2.0\AndroidManifest.xml:21:19-78
127        <activity
127-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:73:9-78:43
128            android:name="com.google.android.gms.ads.AdActivity"
128-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:74:13-65
129            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
129-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:75:13-122
130            android:exported="false"
130-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:76:13-37
131            android:theme="@android:style/Theme.Translucent" />
131-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:77:13-61
132
133        <provider
133-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:80:9-85:43
134            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
134-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:81:13-76
135            android:authorities="com.smg.tractor.trolly.games.farming.game.mobileadsinitprovider"
135-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:82:13-73
136            android:exported="false"
136-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:83:13-37
137            android:initOrder="100" />
137-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:84:13-36
138
139        <service
139-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:87:9-91:43
140            android:name="com.google.android.gms.ads.AdService"
140-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:88:13-64
141            android:enabled="true"
141-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:89:13-35
142            android:exported="false" />
142-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:90:13-37
143
144        <activity
144-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:93:9-97:43
145            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
145-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:94:13-82
146            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
146-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:95:13-122
147            android:exported="false" />
147-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:96:13-37
148        <activity
148-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:98:9-105:43
149            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
149-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:99:13-82
150            android:excludeFromRecents="true"
150-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:100:13-46
151            android:exported="false"
151-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:101:13-37
152            android:launchMode="singleTask"
152-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:102:13-44
153            android:taskAffinity=""
153-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:103:13-36
154            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
154-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:104:13-72
155
156        <meta-data
156-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:107:9-109:36
157            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
157-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:108:13-79
158            android:value="true" />
158-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:109:13-33
159        <meta-data
159-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:110:9-112:36
160            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
160-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:111:13-83
161            android:value="true" />
161-->[com.google.android.gms:play-services-ads-api:24.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\482642211593019ddc82e0ba07d7accc\transformed\jetified-play-services-ads-api-24.2.0\AndroidManifest.xml:112:13-33
162
163        <service
163-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:28:9-34:19
164            android:name="com.google.firebase.components.ComponentDiscoveryService"
164-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:29:13-84
165            android:directBootAware="true"
165-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:34:13-43
166            android:exported="false" >
166-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:30:13-37
167            <meta-data
167-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:31:13-33:85
168                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
168-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:32:17-139
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.android.gms:play-services-measurement-api:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c99f7bb1a9f92c6faab15dc19f03fd1d\transformed\jetified-play-services-measurement-api-21.3.0\AndroidManifest.xml:33:17-82
170            <meta-data
170-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:18:13-20:85
171                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
171-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:19:17-127
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-installations:17.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\e65c07c553ade2f6f82566fd0775e908\transformed\jetified-firebase-installations-17.0.1\AndroidManifest.xml:20:17-82
173        </service>
174
175        <provider
175-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:25:9-30:39
176            android:name="com.google.firebase.provider.FirebaseInitProvider"
176-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:26:13-77
177            android:authorities="com.smg.tractor.trolly.games.farming.game.firebaseinitprovider"
177-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:27:13-72
178            android:directBootAware="true"
178-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:28:13-43
179            android:exported="false"
179-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:29:13-37
180            android:initOrder="100" />
180-->[com.google.firebase:firebase-common:20.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\598c735f0e88d98ade3207a5a286fd60\transformed\jetified-firebase-common-20.3.3\AndroidManifest.xml:30:13-36
181
182        <receiver
182-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:29:9-33:20
183            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
183-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:30:13-85
184            android:enabled="true"
184-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:31:13-35
185            android:exported="false" >
185-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:32:13-37
186        </receiver>
187
188        <service
188-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:35:9-38:40
189            android:name="com.google.android.gms.measurement.AppMeasurementService"
189-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:36:13-84
190            android:enabled="true"
190-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:37:13-35
191            android:exported="false" />
191-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:38:13-37
192        <service
192-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:39:9-43:72
193            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
193-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:40:13-87
194            android:enabled="true"
194-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:41:13-35
195            android:exported="false"
195-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:42:13-37
196            android:permission="android.permission.BIND_JOB_SERVICE" />
196-->[com.google.android.gms:play-services-measurement:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddf0be795485d7766989fa68647469ae\transformed\jetified-play-services-measurement-21.3.0\AndroidManifest.xml:43:13-69
197
198        <meta-data
198-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
199            android:name="com.google.android.gms.version"
199-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
200            android:value="@integer/google_play_services_version" />
200-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\25fa5f1e0a7089bde7eb8bf7c1e9b8ea\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
201
202        <provider
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
203            android:name="androidx.startup.InitializationProvider"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
204            android:authorities="com.smg.tractor.trolly.games.farming.game.androidx-startup"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
206            <meta-data
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
207                android:name="androidx.work.WorkManagerInitializer"
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
208                android:value="androidx.startup" />
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
209            <meta-data
209-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
210-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
211                android:value="androidx.startup" />
211-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\beedf04abc7ce0bcec36576e13b46218\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
214                android:value="androidx.startup" />
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
215        </provider>
216
217        <service
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
218            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
220            android:enabled="@bool/enable_system_alarm_service_default"
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
221            android:exported="false" />
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
222        <service
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
223            android:name="androidx.work.impl.background.systemjob.SystemJobService"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
225            android:enabled="@bool/enable_system_job_service_default"
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
226            android:exported="true"
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
227            android:permission="android.permission.BIND_JOB_SERVICE" />
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
228        <service
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
229            android:name="androidx.work.impl.foreground.SystemForegroundService"
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
231            android:enabled="@bool/enable_system_foreground_service_default"
231-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
233
234        <receiver
234-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
235            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
235-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
237            android:enabled="true"
237-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
238            android:exported="false" />
238-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
239        <receiver
239-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
240-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
245                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
245-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
246                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
246-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
247            </intent-filter>
248        </receiver>
249        <receiver
249-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
250            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
250-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
251            android:directBootAware="false"
251-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
252            android:enabled="false"
252-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
253            android:exported="false" >
253-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
254            <intent-filter>
254-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
255                <action android:name="android.intent.action.BATTERY_OKAY" />
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
255-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
256                <action android:name="android.intent.action.BATTERY_LOW" />
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
256-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
260-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
262            android:enabled="false"
262-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
265                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
265-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
266                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
266-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
267            </intent-filter>
268        </receiver>
269        <receiver
269-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
270            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
270-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
271            android:directBootAware="false"
271-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
272            android:enabled="false"
272-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
273            android:exported="false" >
273-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
274            <intent-filter>
274-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
275                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
275-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
276            </intent-filter>
277        </receiver>
278        <receiver
278-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
279            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
279-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
280            android:directBootAware="false"
280-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
281            android:enabled="false"
281-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
282            android:exported="false" >
282-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
283            <intent-filter>
283-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
284                <action android:name="android.intent.action.BOOT_COMPLETED" />
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
284-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
285                <action android:name="android.intent.action.TIME_SET" />
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
285-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
286                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
286-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
290            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
290-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
292            android:enabled="@bool/enable_system_alarm_service_default"
292-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
295                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
295-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
296            </intent-filter>
297        </receiver>
298        <receiver
298-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
299            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
299-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
300            android:directBootAware="false"
300-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
301            android:enabled="true"
301-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
302            android:exported="true"
302-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
303            android:permission="android.permission.DUMP" >
303-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
304            <intent-filter>
304-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
305                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
305-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d36d9d7663bf6dc4c74476f3c6323ec\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
306            </intent-filter>
307        </receiver>
308
309        <uses-library
309-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
310            android:name="android.ext.adservices"
310-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
311            android:required="false" />
311-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\9b50dd355d85f485e2b0dc52450e5556\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
312        <uses-library
312-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-12:40
313            android:name="org.apache.http.legacy"
313-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:13-50
314            android:required="false" />
314-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:12:13-37
315
316        <meta-data
316-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:9-16:70
317            android:name="com.google.android.gms.ads.APPLICATION_ID"
317-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-69
318            android:value="ca-app-pub-8347415490306764~4325096074" />
318-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-67
319        <meta-data
319-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:9-19:43
320            android:name="com.google.unity.ads.UNITY_VERSION"
320-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:13-62
321            android:value="2021.3.45f1" />
321-->[:unityLibrary:GoogleMobileAdsPlugin.androidlib] D:\My Project\Tractor Simulator Cargo Games (V1.9)smg\Tractor Simulator Cargo\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\GoogleMobileAdsPlugin.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:13-40
322
323        <receiver
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
324            android:name="androidx.profileinstaller.ProfileInstallReceiver"
324-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
325            android:directBootAware="false"
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
326            android:enabled="true"
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
327            android:exported="true"
327-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
328            android:permission="android.permission.DUMP" >
328-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
329            <intent-filter>
329-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
330                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
330-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
330-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
331            </intent-filter>
332            <intent-filter>
332-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
333                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
333-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
333-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
334            </intent-filter>
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
336                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
336-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
337            </intent-filter>
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
339                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
339-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
339-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\60e9f31c893fa9114f97e208ae94e67b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
340            </intent-filter>
341        </receiver>
342
343        <service
343-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
344            android:name="androidx.room.MultiInstanceInvalidationService"
344-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
345            android:directBootAware="true"
345-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
346            android:exported="false" />
346-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\90093cf4e7e2b63e3aab59e75ca2ecb9\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
347    </application>
348
349</manifest>
